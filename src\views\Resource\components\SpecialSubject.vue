<template>
    <div class="special-subject-box right-panel-content">
        <div class="info-title common-gradient-text">武当文化资源库</div>
        <div class="info-totals">
            <div class="total-item" v-for="item in totalDatas" :key="item.label">
                <div class="total-circle">
                    <div class="total-value">
                        <span class="total-number">{{ item.value }}</span>
                        <span class="total-unit">{{ item.unit }}</span>
                    </div>
                </div>
                <div class="total-label">{{ item.label }}</div>
            </div>
        </div>
        <div class="info-content-charts">
            <!-- <div ref="chartLeftRef" class="chart-box"></div>
            <div ref="chartRightRef" class="chart-box"></div> -->
            <img class="demo-echart-img" src="@/assets/images/demo/echart_1.png" />
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">古建筑类型</span>
        </div>
        <div class="info-items">
            <div class="info-items-row">
                <div class="info-item" v-for="item in resourceInfos.slice(0, 4)" :key="item.name">
                    <div class="info-item-value">
                        <span class="value-number">{{ item.value }}</span>
                        <span class="value-unit">{{ item.unit }}</span>
                    </div>
                    <div class="info-item-label">{{ item.name }}</div>
                </div>
            </div>
            <div class="info-items-row">
                <div class="info-item" v-for="item in resourceInfos.slice(4, 8)" :key="item.name">
                    <div class="info-item-value">
                        <span class="value-number">{{ item.value }}</span>
                        <span class="value-unit">{{ item.unit }}</span>
                    </div>
                    <div class="info-item-label">{{ item.name }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { useDasUE } from '@/hooks/useDasUEHook'
const markerBasePath = import.meta.env.VITE_WDS_MARKER_PATH
let ueManager = null
let pointsLayer = ref([]) // 点位图层 pointsLayer

const chartLeftRef = ref()
const chartRightRef = ref()

const resourceInfos = ref([
    { name: '宫殿', value: 312, unit: '个' },
    { name: '庵堂', value: 312, unit: '个' },
    { name: '岩庙', value: 400, unit: '个' },
    { name: '亭台', value: 1612, unit: '件' },
    { name: '楼阁', value: 312, unit: '个' },
    { name: '桥梁', value: 312, unit: '个' },
    { name: '陵墓', value: 400, unit: '个' },
    { name: '其他', value: 400, unit: '个' }
])

const totalDatas = ref([
    { label: '古建数字化率', value: '99.3', unit: '%' },
    { label: '古建数字化', value: '225', unit: '个' },
    { label: '古建总数量', value: '230', unit: '个' }
])

const colorList = ['#FF6A3A', '#FFD03B', '#2BA471', '#FFDA98']

const heritageList = ref([
    {
        name: '1',
        value: '1',
        texturePath: `${markerBasePath}/subject_bihuacaihui.png`,
        points: [[111.11942975937782, 32.50562688088714, 150.13863950448774]]
    },
    {
        name: '2',
        value: '2',
        texturePath: `${markerBasePath}/subject_qishierfeng.png`,
        points: [[111.2111478682384, 32.290780255784064, 469.74324908745285]]
    },
    {
        name: '3',
        value: '3',
        texturePath: `${markerBasePath}/subject_tianzhufeng.png`,
        points: [[110.87398559777553, 32.552662125968354, 375.70797279891065]]
    },
    {
        name: '4',
        value: '4',
        texturePath: `${markerBasePath}/subject_gushumingmu.png`,
        points: [[111.02222289106207, 32.42688727729715, 792.2787811654281]]
    }
])
// 建造年代数据
const buildingAgeData = ref([
    { name: '宋代', value: 33.2 },
    { name: '元代', value: 33.2 },
    { name: '明代', value: 33.2 },
    { name: '清代', value: 33.2 }
])

// 建造风格数据
const buildingStyleData = ref([
    { name: '斗拱', value: 33.2 },
    { name: '石岩', value: 33.2 },
    { name: '琉璃瓦', value: 33.2 },
    { name: '其他', value: 33.2 }
])

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.dasSelectTool.initSelectTool({
            onClick: obj => onBulidingMarkerClick(obj)
        })
        addPoints()
    })
    nextTick(() => {
        initChart(chartLeftRef, '建造年代', buildingAgeData.value)
        initChart(chartRightRef, '建造风格', buildingStyleData.value)
    })
})
// 通用图表初始化方法
const initChart = (chartRef: unknown, title: string, data: unknown[]) => {
    if (!chartRef.value) return null
    chartRef.value?.removeAttribute('_echarts_instance_')
    const instance = echarts.init(chartRef.value)
    const totalNum = data.reduce((acc, item) => acc + item.value, 0)
    const option = {
        backgroundColor: 'transparent',
        title: {
            text: title,
            left: '28%',
            top: '42%',
            textAlign: 'center',
            textStyle: {
                color: '#E1BB82',
                fontSize: 14,
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(225, 187, 130, 0.4)',
            borderColor: '#EEC173',
            padding: 20,
            textStyle: {
                fontSize: 20,
                color: '#ffffff'
            }
        },
        legend: {
            orient: 'vertical',
            right: '0%',
            top: 'center',
            itemWidth: 5,
            itemHeight: 5,
            itemGap: 12,
            icon: 'circle',
            textStyle: {
                rich: {
                    name: {
                        color: '#FFE2B4',
                        fontSize: 12,
                        width: 50
                    },
                    value: {
                        color: '#fff',
                        fontSize: 14,
                        fontWeight: 'bold',
                        width: 50
                    }
                }
            },
            formatter: name => {
                let obj = data.find(item => item.name === name)
                let value = obj?.value || 0
                let percent = totalNum === 0 ? '0%' : ((value / totalNum) * 100).toFixed(1) + '%'
                return `{name|${name}}{value|${percent}}`
            }
        },
        series: [
            {
                type: 'pie',
                radius: ['80%', '90%'],
                center: ['30%', '50%'],
                avoidLabelOverlap: false,
                startAngle: 90,
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                emphasis: {
                    scale: true,
                    scaleSize: 4
                },
                data: data.map((item: { name: string; value: number }, index) => ({
                    name: item.name,
                    value: item.value,
                    itemStyle: {
                        color: colorList[index % colorList.length],
                        borderColor: 'rgba(0, 0, 0, 0.2)',
                        borderWidth: 3
                    }
                }))
            },
            // 内边框的设置
            {
                type: 'pie',
                radius: ['68%', '72%'],
                center: ['30%', '50%'],
                tooltip: {
                    show: false
                },
                emphasis: {
                    scale: false
                },
                labelLine: {
                    show: false
                },
                data: [0],
                itemStyle: {
                    color: '#F9C68D'
                }
            }
        ]
    }
    instance.setOption(option)
    return instance
}

onBeforeUnmount(async () => {
    ueManager?.clearAllLayerAndEvent()
})
// 添加建筑点位
const addPoints = async () => {
    if (!ueManager) return
    let datas = heritageList.value.filter(item => item.points?.length > 0)
    const layers = await ueManager.dasPointsLayer.batchCreateInstance(
        datas.map(item => ({
            ...item,
            pointSize: [150, 38],
            enableDepthTest: false
        }))
    )
    pointsLayer.value = layers.map((layer, index) => {
        layer.customData = datas[index]
        return layer
    })
}
// 标注点击事件
const onBulidingMarkerClick = obj => {
    const item = obj.message ? JSON.parse(obj.message) : {}
    if (item.selectLayer?.class == 'DasPointsLayer') {
        pointsLayer.value.forEach(layer => {
            if (layer.id == item.selectLayer.id) {
                // ElMessageBox.alert('点击了点位：' + layer.customData.name, '标注点击事件')
            }
        })
    }
}
</script>

<style lang="scss" scoped>
.special-subject-box {
    height: 100%;
    text-align: right;

    &.right-panel-content .info-content-title {
        margin-top: 20px;
    }

    .info-item {
        width: 23%;
    }

    .info-content-charts {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 60px 0;

        .chart-box {
            height: 320px;
            width: 690px;
            margin-left: 55px;
        }
        .demo-echart-img {
            width: 1454px;
            height: auto;
        }
    }
}
</style>
