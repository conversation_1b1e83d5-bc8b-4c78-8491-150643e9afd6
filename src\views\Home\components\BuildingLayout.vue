<template>
    <div class="building-layout-box right-panel-content">
        <div class="info-title common-gradient-text">建筑布局</div>
        <div class="info-subtitle">——中轴显礼制，“之”字蕴玄机</div>
        <div class="info-introduce">
            武当山古建筑群以“天人合一”为核心理念，构建了极具道文化特色的空间体系。其布局主要为中轴对称与“之”字形布局，形成独特结构。主要宫观如紫霄宫、玉虚宫等沿中轴线布局，体现皇家礼制；登山香道则顺应山势呈“之”字形盘旋，暗合“道法自然”思想。彰显明代皇家建筑气度，营造“仙山琼阁”意境，是中国古代山地建筑的典范。
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text" style="letter-spacing: 4px">建筑布局概况</span>
        </div>
        <div class="info-items">
            <div class="info-items-row">
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-number">{{ buildingInfo.axisBuildings }}</span>
                        <span class="value-unit">个</span>
                    </div>
                    <div class="info-item-label">中轴对称建筑</div>
                </div>
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-number">{{ buildingInfo.zigzagBuildings }}</span>
                        <span class="value-unit">个</span>
                    </div>
                    <div class="info-item-label">"之"字对称建筑</div>
                </div>
            </div>
            <div class="info-items-row">
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-number">{{ buildingInfo.concept }}</span>
                    </div>
                    <div class="info-item-label">建筑理念</div>
                </div>
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-number">{{ buildingInfo.atmosphere }}</span>
                    </div>
                    <div class="info-item-label">构造意境</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const buildingInfo = ref({
    axisBuildings: 12,
    zigzagBuildings: 400,
    concept: '天人合一，道法自然',
    atmosphere: '仙山琼阁'
})
</script>

<style lang="scss" scoped>
.building-layout-box {
    height: 100%;
    text-align: right;

    .info-item {
        width: 40%;
    }
}
</style>
