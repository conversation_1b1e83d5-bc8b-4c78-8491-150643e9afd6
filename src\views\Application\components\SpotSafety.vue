<template>
    <div class="safety-panel-box right-panel-content">
        <div class="info-content-title">
            <span class="info-content-title-text">应急设备</span>
        </div>
        <div class="equipment-content">
            <div class="equipment-item" v-for="(item, index) in equipmentList" :key="index">
                <div class="equipment-inner">
                    <div class="equipment-icon" :class="'icon-' + item.key"></div>
                    <div class="equipment-text">
                        <div class="equipment-label">{{ item.name }}</div>
                        <img class="equipment-hr" src="@/assets/images/resource/hr_line.png" />
                        <div>
                            <span class="equipment-value">{{ item.value }}</span>
                            <span class="equipment-unit">{{ item.unit }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">应急力量</span>
        </div>
        <div class="security-content">
            <div ref="chartLeftRef" class="chart-box"></div>
            <div class="plan-list">
                <div class="plan-item" v-for="(item, index) in planList" :key="index">
                    <img src="@/assets/images/application/icon_plan.png" />
                    <div class="plan-right-info">
                        <div class="plan-name">{{ item.name }}</div>
                        <div class="plan-info">
                            <span class="plan-time">发布时间:2023-2</span>
                            <span class="plan-more">查看详情>></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">应急场景</span>
        </div>
        <div class="theme-list">
            <div class="theme-card" v-for="(item, index) in emergencyList" :key="index">
                <div class="theme-card-name">{{ item.name }}</div>
                <div class="theme-card-desc">{{ item.desc }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'

let baseWidth = 3840
const chartLeftRef = ref()
let chartLeftInstance = null
const colorList = ['#FF6A3A', '#FFD03B', '#2BA471', '#FFDA98']
const equipmentList = ref([
    { name: '视频监控', value: '100', unit: '个', key: 'video' },
    { name: '消防设备', value: '100', unit: '个', key: 'fire' },
    { name: '微型消防站', value: '100', unit: '个', key: 'microfire' },
    { name: '游客服务中心', value: '100', unit: '个', key: 'serviceCenter' },
    { name: '警务室', value: '100', unit: '个', key: 'police' },
    { name: '医务室', value: '100', unit: '个', key: 'medical' }
])
const personData = ref([
    { name: '安保人员', value: 10 },
    { name: '景区工作人员', value: 10 },
    { name: '志愿者', value: 10 },
    { name: '其他', value: 10 }
])
const emergencyList = ref([
    { name: '临崖预警', desc: '整合1300路监控，覆盖临崖危险区，红外遥感系统10分钟内响应游客越界' },
    { name: '人流拥堵', desc: '整合1300路监控，覆盖临崖危险区，红外遥感系统10分钟内响应游客越界' },
    { name: '雷电预警', desc: '整合1300路监控，覆盖临崖危险区，红外遥感系统10分钟内响应游客越界' },
    { name: '火灾监测', desc: '整合1300路监控，覆盖临崖危险区，红外遥感系统10分钟内响应游客越界' }
])
const planList = ref([
    { name: '《武当山自然灾害专项应急预案应急预案应急预案应急预案' },
    { name: '《武当山自然灾害专项应急预案》' }
])

onMounted(() => {
    nextTick(() => {
        chartLeftInstance = initChart(chartLeftRef, '人员总数', personData.value)
    })
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
})

onBeforeUnmount(async () => {
    // 移除事件监听器
    window.removeEventListener('resize', handleResize)
    // 销毁图表实例
    if (chartLeftInstance) {
        chartLeftInstance.dispose()
        chartLeftInstance = null
    }
})
// 计算响应式字体大小
const getResponsiveFontSize = (baseSize: number) => {
    const screenWidth = window.innerWidth
    const scale = screenWidth / baseWidth
    return Math.max(Math.round(baseSize * scale), 10) // 最小字体10px
}

// 防抖函数
const debounce = (func, wait: number) => {
    let timeout: NodeJS.Timeout
    return function executedFunction(...args: any[]) {
        const later = () => {
            clearTimeout(timeout)
            func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
    }
}

// 处理窗口大小变化
const handleResize = debounce(() => {
    if (chartLeftInstance) {
        chartLeftInstance.dispose()
        chartLeftInstance = initChart(chartLeftRef, '人员总数', personData.value)
    }
}, 300)

// 通用图表初始化方法
const initChart = (chartRef: unknown, title: string, data: unknown[]) => {
    if (!chartRef.value) return null
    chartRef.value?.removeAttribute('_echarts_instance_')
    const instance = echarts.init(chartRef.value)
    const totalNum = data.reduce((acc, item) => acc + item.value, 0)
    const option = {
        backgroundColor: 'transparent',
        title: {
            text: title,
            left: '20%',
            top: '43%',
            textAlign: 'center',
            textStyle: {
                color: '#E1BB82',
                fontSize: getResponsiveFontSize(14),
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(225, 187, 130, 0.4)',
            borderColor: '#EEC173',
            padding: 20,
            textStyle: {
                fontSize: getResponsiveFontSize(20),
                color: '#ffffff'
            }
        },
        legend: {
            orient: 'vertical',
            right: '0%',
            top: 'center',
            itemWidth: Math.max(Math.round(5 * (window.innerWidth / baseWidth)), 3),
            itemHeight: Math.max(Math.round(5 * (window.innerWidth / baseWidth)), 3),
            itemGap: Math.max(Math.round(12 * (window.innerWidth / baseWidth)), 8),
            icon: 'circle',
            textStyle: {
                rich: {
                    name: {
                        color: '#FFE2B4',
                        fontSize: getResponsiveFontSize(12),
                        width: Math.max(Math.round(80 * (window.innerWidth / baseWidth)), 30)
                    },
                    value: {
                        color: '#fff',
                        fontSize: getResponsiveFontSize(14),
                        fontWeight: 'bold',
                        width: Math.max(Math.round(50 * (window.innerWidth / baseWidth)), 30)
                    }
                }
            },
            formatter: name => {
                let obj = data.find(item => item.name === name)
                let value = obj?.value || 0
                let percent = totalNum === 0 ? '0%' : ((value / totalNum) * 100).toFixed(1) + '%'
                return `{name|${name}}{value|${percent}}`
            }
        },
        series: [
            {
                type: 'pie',
                radius: ['60%', '70%'],
                center: ['22%', '50%'],
                avoidLabelOverlap: false,
                startAngle: 90,
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                emphasis: {
                    scale: true,
                    scaleSize: 4
                },
                data: data.map((item: { name: string; value: number }, index) => ({
                    name: item.name,
                    value: item.value,
                    itemStyle: {
                        color: colorList[index % colorList.length],
                        borderColor: 'rgba(0, 0, 0, 0.2)',
                        borderWidth: 3
                    }
                }))
            },
            // 内边框的设置
            {
                type: 'pie',
                radius: ['50%', '53%'],
                center: ['22%', '50%'],
                tooltip: {
                    show: false
                },
                emphasis: {
                    scale: false
                },
                labelLine: {
                    show: false
                },
                data: [0],
                itemStyle: {
                    color: '#F9C68D'
                }
            }
        ]
    }
    instance.setOption(option)
    return instance
}
</script>

<style lang="scss" scoped>
.safety-panel-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: left;
    &.right-panel-content .info-content-title {
        margin-top: 0px;
    }
    .info-small-subtitle {
        justify-content: flex-end;
    }
    .equipment-content {
        font-family: 'Source Han Sans CN';
        width: 1400px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-bottom: 40px;
        .equipment-item {
            display: inline-block;
            margin-top: 40px;
            width: 427px;
            height: 172px;
            background: linear-gradient(
                90deg,
                rgba(167, 148, 90, 0.06) 0%,
                rgba(189, 168, 104, 0.3) 100%
            );
        }
        .equipment-inner {
            width: 100%;
            height: 100%;
            padding: 0 32px;
            display: flex;
            align-items: center;
            background: url('@/assets/images/application/bg_border.png') center center / 100%
                no-repeat;
            color: #fff;
            .equipment-icon {
                width: 125px;
                height: 125px;
                margin-right: 20px;
                &.icon-video {
                    background: url('@/assets/images/application/icon_video.png') center center /
                        100% 100% no-repeat;
                }
                &.icon-fire {
                    background: url('@/assets/images/application/icon_fire.png') center center /
                        100% 100% no-repeat;
                }
                &.icon-microfire {
                    background: url('@/assets/images/application/icon_microfire.png') center center/
                        100% 100% no-repeat;
                }
                &.icon-serviceCenter {
                    background: url('@/assets/images/application/icon_serviceCenter.png') center
                        center/ 100% 100% no-repeat;
                }
                &.icon-police {
                    background: url('@/assets/images/application/icon_police.png') center center/
                        100% 100% no-repeat;
                }
                &.icon-medical {
                    background: url('@/assets/images/application/icon_medical.png') center center/
                        100% 100% no-repeat;
                }
            }
            .equipment-text {
                text-align: left;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            .equipment-label {
                font-size: 30px;
            }
            .equipment-hr {
                width: 204.621px;
                height: 3px;
                margin: 15px 0 5px 0;
            }
            .equipment-value {
                font-size: 50px;
                font-weight: 600;
            }
            .equipment-unit {
                font-size: 32px;
                margin-left: 10px;
            }
        }
    }
    .security-content {
        width: 1500px;
        height: 420px;
        display: flex;
        .chart-box {
            width: 725px;
            height: 100%;
        }
        .plan-list {
            width: calc(100% - 775px);
            margin-left: 50px;
            .plan-item {
                width: 100%;
                height: 160px;
                color: #fff;
                border-radius: 8px;
                border: 1px solid #e1bb82aa;
                background: rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(8px);
                margin-top: 20px;
                display: flex;
                align-items: center;
                padding: 25px;
                img {
                    width: 80px;
                    height: 80px;
                    margin-right: 25px;
                }
                .plan-right-info {
                    width: calc(100% - 105px);
                }
                .plan-name {
                    font-size: 30px;
                    color: #ffe2b4;
                    max-width: 100%;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
                .plan-info {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;
                }
                .plan-time {
                    font-size: 24px;
                }
                .plan-more {
                    font-size: 18px;
                    padding: 5px 10px;
                    border-radius: 4px;
                    border: 1px solid rgba(255, 229, 150, 0.5);
                    background: rgba(255, 229, 150, 0.2);
                    cursor: pointer;
                }
            }
        }
    }
    .theme-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        color: #fff;
        .theme-card {
            position: relative;
            width: 400px;
            height: 230px;
            border-radius: 10px;
            margin-top: 20px;
            background: url('@/assets/images/demo/cover_3.png') center center/ 100% 100% no-repeat;
            display: flex;
            align-items: center;
            cursor: pointer;
            overflow: hidden;
            margin-left: 20px;
            .theme-card-name {
                width: 100%;
                position: absolute;
                bottom: 0px;
                left: 0px;
                font-size: 30px;
                height: 48px;
                background: rgba(0, 0, 0, 0.8);
                padding-left: 20px;
            }
            .theme-card-desc {
                height: 100%;
                font-size: 24px;
                text-align: center;
                padding: 48px;
                background: #00000066;
                display: none;
            }
            &:hover {
                border: 1px solid #e1bb82;
                .theme-card-desc {
                    display: block;
                }
            }
        }
    }
}
</style>
