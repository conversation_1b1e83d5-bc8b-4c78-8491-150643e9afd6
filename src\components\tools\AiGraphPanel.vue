<template>
    <div class="ai-graph-panel common-modal-border">
        <img
            class="graph-close"
            @click="emit('close')"
            src="@/assets/images/common/icon_close.png"
        />
        <div class="left-menu">
            <img src="@/assets/images/common/icon_up.png" />
            <div class="menu-list"></div>
            <img src="@/assets/images/common/icon_down.png" />
        </div>
        <div class="graph-box">
            <div>关系图谱</div>
            <img src="@/assets/images/demo/demo_graph.png" />
        </div>
        <div class="right-info">
            <div class="info-title">太和宫高清影像</div>
            <img class="info-image" src="@/assets/images/demo/cover_3.png" />
        </div>
    </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close'])
const buildingList = ref([
    { name: '太和宫', id: 1 },
    { name: '遇真宫', id: 2 },
    { name: '玉虚宫', id: 3 },
    { name: '南岩宫', id: 4 }
])
</script>

<style lang="scss" scoped>
.ai-graph-panel {
    position: relative;
    z-index: 1500;
    width: 1769px;
    height: 1023px;
    .graph-close {
        width: 140px;
        height: 140px;
        position: absolute;
        top: -70px;
        right: -70px;
        cursor: pointer;
    }
    。graph-box {}
}
</style>
