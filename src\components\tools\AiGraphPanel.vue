<template>
    <div class="ai-graph-panel common-modal-border">
        <img
            class="graph-close"
            @click="emit('close')"
            src="@/assets/images/common/icon_close.png"
        />
        <div class="left-menu">
            <img class="nav-arrow" src="@/assets/images/common/icon_up.png" />
            <div class="menu-list">
                <div
                    v-for="item in buildingList"
                    :key="item.id"
                    class="menu-item"
                    :class="{ active: selectedId === item.id }"
                    @click="selectBuilding(item.id)"
                >
                    <span class="menu-text">{{ item.name }}</span>
                </div>
            </div>
            <img class="nav-arrow" src="@/assets/images/common/icon_down.png" />
        </div>
        <div class="graph-box">
            <div class="graph-title">关系图谱</div>
            <img class="graph-image" src="@/assets/images/demo/demo_graph.png" />
        </div>
        <div class="right-info">
            <div class="info-title">太和宫高清影像</div>
            <img class="info-image" src="@/assets/images/demo/cover_3.png" />
            <div class="info-details">
                <div class="info-row">
                    <span class="info-label">影像名称</span>
                    <span class="info-value">太和殿</span>
                </div>
                <div class="info-row">
                    <span class="info-label">上传人员</span>
                    <span class="info-value">张三</span>
                </div>
                <div class="info-row">
                    <span class="info-label">分类</span>
                    <span class="info-value">建筑</span>
                </div>
                <div class="info-row">
                    <span class="info-label">关键词</span>
                    <span class="info-value">张三丰，真武大帝</span>
                </div>
                <div class="info-row">
                    <span class="info-label">描述</span>
                    <span class="info-value">
                        这是一段太和宫的描述，这是一段太和宫的描述，这是一段太和宫的描述，这是一段太和宫的描述...
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close'])

const buildingList = ref([
    { name: '太和宫', id: 1 },
    { name: '金殿', id: 2 },
    { name: '金殿', id: 3 },
    { name: '金殿', id: 4 }
])

const selectedId = ref(1)

const selectBuilding = (id: number) => {
    selectedId.value = id
}
</script>

<style lang="scss" scoped>
.ai-graph-panel {
    position: relative;
    z-index: 1500;
    width: 1769px;
    height: 1023px;
    display: flex;
    padding: 40px;
    box-sizing: border-box;
    backdrop-filter: blur(14px);
    background: #21201dcc;

    .graph-close {
        width: 140px;
        height: 140px;
        position: absolute;
        top: -70px;
        right: -70px;
        cursor: pointer;
    }

    .left-menu {
        width: 228px;
        margin-top: 70px;
        text-align: center;
        margin-right: 40px;

        .nav-arrow {
            width: 40px;
            height: 40px;
            cursor: pointer;
            margin: 10px 0;
        }

        .menu-list {
            flex: 1;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 10px;

            .menu-item {
                width: 228px;
                height: 66px;
                background-image: url('@/assets/images/common/title_label.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                margin: 25px 0;

                &.active {
                    color: #fff;
                    background-image: url('@/assets/images/common/title_label_active.png');
                }

                .menu-text {
                    color: #e1bb82;
                    font-size: 24px;
                    font-weight: 500;
                }
            }
        }
    }

    .graph-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 40px;

        .graph-title {
            color: #e1bb82;
            font-size: 41px;
            font-weight: 600;
            margin-bottom: 40px;
        }

        .graph-image {
            width: 797px;
            height: 770px;
            object-fit: contain;
        }
    }

    .right-info {
        width: 422px;
        height: 625px;
        padding: 30px;
        margin-top: 70px;
        border-radius: 5px;
        border: 1px solid #ffe7c421;
        background: linear-gradient(
            155deg,
            rgba(175, 167, 142, 0.8) -61.8%,
            rgba(141, 130, 99, 0.8) -37.56%,
            rgba(74, 65, 39, 0.8) -3.93%,
            rgba(17, 15, 9, 0.8) 39.86%,
            rgba(33, 32, 29, 0.8) 100.85%
        );

        backdrop-filter: blur(4px);

        .info-title {
            color: #e1bb82;
            font-size: 25px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .info-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info-details {
            flex: 1;

            .info-row {
                display: flex;
                margin-bottom: 15px;
                align-items: flex-start;

                .info-label {
                    color: #e1bb82;
                    font-size: 18px;
                    font-weight: 500;
                    width: 80px;
                    flex-shrink: 0;
                    margin-right: 10px;
                }

                .info-value {
                    color: #ffffff;
                    font-size: 18px;
                    line-height: 1.4;
                    flex: 1;
                    word-break: break-all;
                    text-align: right;
                }
            }
        }
    }
}
</style>
