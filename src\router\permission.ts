import router from '.'

// 在导出 router 前添加
router.beforeEach((to, from, next) => {
    // 示例：从 localStorage 或 Vuex 中获取用户权限
    const userPermissions = JSON.parse(localStorage.getItem('permissions')) || []
    console.log(userPermissions)

    // 若访问的是需要权限的路由且用户无权限
    // if (to.meta?.permission && !userPermissions.includes(to.meta.permission)) {
    //     next('/login') // 重定向到登录页
    // } else {
    //     next() // 允许访问
    // }
    next()
})
