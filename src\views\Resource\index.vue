<template>
    <div class="resource-container">
        <RightPanelBox>
            <template #content>
                <ResourceTotals v-if="currentNav === 'resource'" />
                <SpecialSubject v-else-if="currentNav === 'specialSubject'" />
            </template>
        </RightPanelBox>
        <ResourceSelector v-if="currentNav === 'specialSubject'" />
        <RelicInfos v-if="isShowRelicInfos" @close="isShowRelicInfos = false" />
        <BottomMenu type="resource" @change-nav="onChangeNav" />
    </div>
</template>
<script setup lang="ts">
import BottomMenu from '@/components/tools/BottomMenu.vue'
import RightPanelBox from '@/components/commonPanel/RightPanelBox.vue'
import ResourceTotals from './components/ResourceTotals.vue'
import SpecialSubject from './components/SpecialSubject.vue'
import ResourceSelector from './components/ResourceSelector.vue'
import RelicInfos from './components/RelicInfos.vue'
import { useNavStore } from '@/stores/nav'

const navStore = useNavStore()
const currentNav = computed(() => navStore.currentNav) // 当前选中的二级菜单
const isShowRelicInfos = ref(false)
// 切换子菜单
const onChangeNav = (type: string) => {
    switch (type) {
        case 'resource':
            break
        case 'specialSubject':
            break
        case 'relicInfos':
            isShowRelicInfos.value = true
            break
    }
}
</script>
<style scoped lang="scss"></style>
