import ExcuteUEFun from '@/components/dasUE/ExcuteUEFun'
import DasBaseLayer from '@/components/dasUE/layer/DasBaseLayer'
import Das<PERSON>BillboardLayer from '@/components/dasUE/layer/DasUIBillboardLayer'

export default class DasPointsLayer extends DasBaseLayer {
    public popupLayer
    constructor() {
        super()
        this.id = ''
        this.className = 'DasPointsLayer'
        this.popupLayer = null
    }

    /**
     * Set points using LLH (Longitude, Latitude, Height) coordinates
     * @param {Array} points - Array of [longitude, latitude, height] arrays
     * @returns {Promise<boolean>} - Success result
     */
    setPointsLLH = async points => {
        const param = { points: points }
        return await this.excuteUEClassFunction('setPointsLLH', param)
    }

    /**
     * Get points in LLH (Longitude, Latitude, Height) format
     * @returns {Promise<Array>} - Array of [longitude, latitude, height] arrays
     */
    getPointsLLH = async () => {
        let points = []
        await this.excuteUEClassFunction('getPointsLLH', {}, function (json) {
            points = json.points
        })
        return points
    }

    /**
     * Set texture for the points
     * @param {string} texturePath - Path to the texture
     * @returns {Promise<boolean>} - Success result
     */
    setTexture = async texturePath => {
        const param = { texturePath: texturePath }
        return await this.excuteUEClassFunction('setTexture', param)
    }

    /**
     * Set point appearance info (color, size, offset)
     * @param {Object} pointInfo - Point appearance configuration with any of these properties directly in the object:
     * @param {Array<number>} [pointInfo.color] - Color as [r, g, b, a] where each value is 0-1
     * @param {Array<number>} [pointInfo.pointSize] - Size as [width, height]
     * @param {Array<number>} [pointInfo.offset] - Offset as [x, y]
     * @returns {Promise<boolean>} - Success result
     */
    setPointInfo = async pointInfo => {
        const param = { pointInfo: pointInfo }
        return await this.excuteUEClassFunction('setPointInfo', param)
    }

    /**
     * Enable or disable depth testing for points
     * @param {boolean} enableDepthTest - Whether to enable depth testing
     * @returns {Promise<boolean>} - Success result
     */
    setDepthTest = async enableDepthTest => {
        const param = { enableDepthTest: enableDepthTest }
        return await this.excuteUEClassFunction('setDepthTest', param)
    }

    /**
     * Update all properties at once
     * @param {Object} config - Combined configuration object with any of these properties:
     * @param {Array} [config.points] - Array of [longitude, latitude, height] arrays
     * @param {string} [config.texturePath] - Path to the texture
     * @param {Array<number>} [config.color] - Color as [r, g, b, a] where each value is 0-1
     * @param {Array<number>} [config.pointSize] - Size as [width, height]
     * @param {Array<number>} [config.offset] - Offset as [x, y]
     * @param {boolean} [config.enableDepthTest] - Whether to enable depth testing
     * @returns {Promise<boolean>} - Success result
     */
    updateAll = async config => {
        return await this.excuteUEClassFunction('updateAll', config)
    }

    /**
     * Create a new points layer instance
     * @param {Object} param - Creation parameters
     * @param {Array} [param.points] - Array of [longitude, latitude, height] arrays
     * @param {string} [param.texturePath] - Path to the texture
     * @param {Array<number>} [param.color] - Color as [r, g, b, a] where each value is 0-1
     * @param {Array<number>} [param.pointSize] - Size as [width, height]
     * @param {Array<number>} [param.offset] - Offset as [x, y]
     * @param {boolean} [param.enableDepthTest] - Whether to enable depth testing
     * @returns {Promise<DasPointsLayer>} - New points layer instance
     */
    async createInstance(param) {
        let pointsLayer = null
        await ExcuteUEFun.excuteUEFunction(
            this.className,
            'createInstance',
            { param: param },
            function (json) {
                pointsLayer = new DasPointsLayer()
                pointsLayer.readObjectInfo(json.object)
            }
        )
        return pointsLayer
    }

    /**
     * Batch create multiple DasPointsLayer instances.
     * @param {Array} params - Array of creation parameters, each parameter is the same as the createInstance method.
     * @returns {Promise<Array>} - Array of created layer objects.
     */
    async batchCreateInstance(params) {
        let layers = []
        await ExcuteUEFun.excuteUEFunction(
            this.className,
            'batchCreateInstance',
            { params: params },
            function (json) {
                if (Array.isArray(json.layers)) {
                    layers = json.layers.map(obj => {
                        const layer = new DasPointsLayer()
                        layer.readObjectInfo(obj)
                        return layer
                    })
                }
            }
        )
        return layers
    }

    /**
     * 同时创建点位 + 弹窗，弹窗对象存在 popupLayer 中
     * @param params
     * @param {Array} [params.dataPoints] - 点位配置数组
     * @param {Array} [params.dataPopups] - 弹窗配置数组
     * @param {Function} [params.onMessage] - 弹窗内点击事件
     * @returns {Promise<Array>} - Array of created layer objects.
     */
    async batchCreateInstanceWithPopup(params) {
        let layers = []
        await ExcuteUEFun.excuteUEFunction(
            this.className,
            'batchCreateInstance',
            { params: params.dataPoints },
            function (json) {
                if (Array.isArray(json.layers)) {
                    layers = json.layers.map(obj => {
                        const layer = new DasPointsLayer()
                        layer.readObjectInfo(obj)
                        return layer
                    })
                }
            }
        )
        Promise.all(
            params.dataPopups.map(item => {
                return new DasUIBillboardLayer().createInstanceWithListener({
                    ...item,
                    onMessage: params.onMessage
                })
            })
        ).then(res => {
            res.forEach((popupLayer, index) => {
                popupLayer.pointId = layers[index].id
                layers[index].popupLayer = popupLayer
            })
        })
        return layers
    }
}
