import DasUE from '@/components/dasUE/DasUE'

let dasUE: DasUE
const domContainerId = 'scene-viewer-container'

export const useDasUE = () => {
    if (!dasUE) {
        console.log('dasUE is null')
        dasUE = new DasUE(domContainerId)
    } else {
        console.log('dasUE is already initialized')
    }
    const onViewerReady = (callback: () => void) => {
        const intervalId = setInterval(() => {
            // 判断此时viewer是否已经初始化完成
            if (dasUE?.isViewerReady) {
                callback()
                clearInterval(intervalId)
            }
        }, 100)
    }
    return {
        dasUE,
        onViewerReady
    }
}
