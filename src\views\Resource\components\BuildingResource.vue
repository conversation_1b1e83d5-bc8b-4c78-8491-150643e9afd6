<template>
    <div class="building-resource-container">
        <div class="modal-mask"></div>
        <div class="modal-box common-modal-border">
            <img
                class="modal-close"
                src="@/assets/images/common/icon_close.png"
                @click="closeModal"
            />
            <div class="modal-container">
                <div class="modal-menu">
                    <div class="menu-title">探索</div>
                    <div
                        v-for="(item, index) in menuList"
                        :key="index"
                        class="menu-item"
                        :class="{ active: activeMenuType == item.type }"
                        @click="changeMenuType(item.type)"
                    >
                        <img :src="item.icon" />
                        <span>{{ item.name }}</span>
                    </div>
                </div>
                <div class="modal-content">
                    <div class="content-title">{{ menuTitle }}</div>
                    <div class="type-list">
                        <div
                            v-for="item in typeList"
                            :key="item.id"
                            @click="changeType(item.id)"
                            class="type-item"
                            :class="{ active: activeType == item.id }"
                        >
                            {{ item.name }}
                        </div>
                    </div>
                    <div class="resource-list">
                        <div v-for="item in resourceList" :key="item.id" class="resource-item">
                            <div class="resource-cover">
                                <div class="resource-tag">法器</div>
                            </div>
                            <div class="resource-name">
                                <img src="@/assets/images/common/icon_title_left.png" alt="" />
                                <span class="resource-name-text">{{ item.name }}</span>
                                <img src="@/assets/images/common/icon_title_right.png" alt="" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import iconBuilding from '@/assets/images/resource/icon_building.png'
import iconRelic from '@/assets/images/resource/icon_relic.png'
import iconCulture from '@/assets/images/resource/icon_culture.png'

let activeMenuType = ref(1)
let activeType = ref(1)
const emit = defineEmits(['close'])
const props = defineProps({
    type: {
        type: Number,
        default: 1
    }
})
const menuList = [
    { name: '建筑风貌', type: 1, icon: iconBuilding },
    { name: '文物新语', type: 2, icon: iconRelic },
    { name: '道脉仙踪', type: 3, icon: iconCulture }
]
const typeList = ref([
    { name: '壁画彩绘', id: 1 },
    { name: '造像', id: 2 },
    { name: '碑刻石刻', id: 3 },
    { name: '砖雕', id: 4 }
])
const menuTitle = computed(() => {
    return menuList.find(item => item.type === activeMenuType.value).name
})
const resourceList = ref(
    Array.from({ length: 8 }).map((item, index) => {
        return {
            id: index + 1,
            name: `明代铜铸真武坐像123123${index + 1}`
        }
    })
)
onMounted(() => {
    activeMenuType.value = props.type || 1
})

// 切换左侧类型
const changeMenuType = type => {
    activeMenuType.value = type
}

const changeType = type => {
    activeType.value = type
}

// 关闭弹窗
const closeModal = () => {
    emit('close')
}
</script>
<style scoped lang="scss">
.building-resource-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    z-index: 1002;
    color: #e1bb82;
    .modal-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: #00000099;
        backdrop-filter: blur(25px);
    }
    .modal-box {
        position: absolute;
        width: 2128px;
        height: 1600px;
        padding: 43px 54px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #21201dcc;
        border-radius: 17px;
        &::before {
            border-radius: 17px;
        }
        .modal-close {
            position: absolute;
            right: -50px;
            top: -50px;
            width: 100px;
            height: 100px;
            cursor: pointer;
            &:hover {
                filter: brightness(1.2);
            }
        }
        .modal-container {
            display: flex;
            height: 100%;
            .modal-menu {
                width: 480px;
                height: 100%;
                padding-right: 50px;
                border-right: 1px solid #e1bb8233;
                .menu-title {
                    font-size: 50px;
                    font-weight: 600;
                    text-align: center;
                }
                .menu-item {
                    transition: all 0.3s ease;
                    cursor: pointer;
                    font-size: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 90px;
                    margin-top: 20px;
                    border-radius: 20px;
                    img {
                        width: 50px;
                        height: 50px;
                        margin-right: 40px;
                    }
                    &.active {
                        background: #e1bb8233;
                    }
                }
            }
            .modal-content {
                width: calc(100% - 480px);
                padding-left: 50px;
                .content-title {
                    text-align: center;
                    font-size: 50px;
                    font-weight: 600;
                    padding-bottom: 45px;
                    margin-bottom: 60px;
                    background: url('@/assets/images/common/underline_title.png') center bottom /
                        892px 34px no-repeat;
                }
                .type-list {
                    width: 100%;
                    display: flex;
                    justify-content: space-around;
                    font-size: 33px;
                    .type-item {
                        transition: all 0.5s ease;
                        cursor: pointer;
                        width: 216px;
                        height: 91px;
                        text-align: center;
                        line-height: 91px;
                        background: url('@/assets/images/common/bg_type.png') center center / 100%
                            100% no-repeat;
                        &.active {
                            color: #fff;
                            background: url('@/assets/images/common/bg_type_active.png') center
                                center / 100% 100% no-repeat;
                        }
                    }
                }
                .resource-list {
                    .resource-item {
                        display: inline-block;
                        margin-top: 90px;
                        &:not(:nth-child(4n)) {
                            margin-right: 50px;
                        }
                        .resource-cover {
                            position: relative;
                            width: 330px;
                            height: 266px;
                            background: url('@/assets/images/demo/cover_3.png') center center /
                                cover no-repeat;
                            margin-bottom: 10px;
                            .resource-tag {
                                position: absolute;
                                color: #fff;
                                font-size: 20px;
                                background: rgba(223, 171, 2, 0.3);
                                padding: 5px 10px;
                                border-radius: 6.5px;
                                left: 10px;
                                top: 10px;
                            }
                        }
                        .resource-name {
                            display: flex;
                            align-items: center;
                            font-size: 29px;
                            color: #e1bb82;
                            .resource-name-text {
                                width: 270px;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                white-space: nowrap;
                            }
                            img {
                                width: 32px;
                                height: 32px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
