<template>
    <div class="resource-selector-box">
        <div class="resource-types">
            <div class="resource-type-title">自由探索</div>
            <div
                class="resource-type-item"
                v-for="item in resTypes"
                :key="item.id"
                :class="{ active: currentResType == item.id }"
                @click="changeResType(item.id)"
            >
                <span>{{ item.name }}</span>
                <span>(200)</span>
            </div>
        </div>
        <img class="left-mist-icon" src="@/assets/images/common/icon_mist_left.png" />
        <img class="right-mist-icon" src="@/assets/images/common/icon_mist_right.png" />
        <div class="resource-list-container">
            <div class="resource-buildings">
                <div
                    class="building-item"
                    v-for="item in buildingData"
                    :key="item.id"
                    :class="{ active: currentBuildingId == item.id }"
                    @click="changeResource(item.id)"
                >
                    {{ item.name }}
                </div>
            </div>
            <Swiper
                class="resource-list"
                :slides-per-view="7"
                :space-between="17"
                :navigation="{
                    prevEl: '.swiper-prev-icon',
                    nextEl: '.swiper-next-icon'
                }"
                :modules="swiperModules"
            >
                <SwiperSlide class="resource-item" v-for="item in resourceData" :key="item.id">
                    <div class="resource-item-cover"></div>
                    <div class="resource-item-name">{{ item.name }}</div>
                </SwiperSlide>
            </Swiper>
            <img class="swiper-prev-icon" src="@/assets/images/common/arrow_left.png" />
            <img class="swiper-next-icon" src="@/assets/images/common/arrow_right.png" />
        </div>
    </div>
</template>
<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Navigation } from 'swiper/modules'
import 'swiper/css/navigation'
const swiperModules = [Navigation]

const currentResType = ref(1)
const currentBuildingId = ref(1)

const resTypes = ref([
    { name: '古建筑群', id: 1 },
    { name: '壁画彩绘', id: 2 },
    { name: '造像', id: 3 },
    { name: '碑刻石刻', id: 4 },
    { name: '砖雕', id: 5 },
    { name: '民俗传说', id: 6 },
    { name: '古树名木', id: 7 },
    { name: '七十二峰', id: 8 }
])
const buildingData = ref([
    { name: '七十二峰', id: 1 },
    { name: '三十六岩', id: 2 },
    { name: '二十四涧', id: 3 },
    { name: '十一洞', id: 4 },
    { name: '三潭', id: 5 },
    { name: '九泉', id: 6 },
    { name: '十池', id: 7 },
    { name: '九井', id: 8 },
    { name: '十石', id: 9 },
    { name: '九台', id: 10 }
])
const resourceData = ref([
    { name: '山门', id: 1 },
    { name: '山门', id: 2 },
    { name: '山门', id: 3 },
    { name: '山门', id: 4 },
    { name: '山门', id: 5 },
    { name: '山门', id: 6 },
    { name: '山门', id: 7 },
    { name: '山门', id: 8 },
    { name: '山门', id: 9 },
    { name: '山门', id: 10 }
])

// 切换类型
const changeResType = type => {
    currentResType.value = type
}
const changeResource = id => {
    currentBuildingId.value = id
}
</script>
<style scoped lang="scss">
.resource-selector-box {
    position: absolute;
    width: 3000px;
    height: 450px;
    z-index: 1500;
    left: 50%;
    bottom: 97px;
    transform: translateX(-50%);
    background: rgba(33, 32, 29, 0.8);
    backdrop-filter: blur(8px);
    border-radius: 8px;
    padding: 25px 150px;
    .left-mist-icon {
        position: absolute;
        width: 136px;
        height: 129px;
        top: -30px;
        left: -90px;
    }
    .right-mist-icon {
        position: absolute;
        width: 136px;
        height: 129px;
        top: -30px;
        right: -90px;
    }
    .resource-types {
        position: absolute;
        top: -155px;
        right: 0;
        width: 3000px;
        height: 136px;
        background: url('@/assets/images/resource/bg_resource_types.png') center center / 100% 100%
            no-repeat;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        padding: 0 180px;
        .resource-type-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 80px;
            font-family: YuWeiXingShu;
            color: #fff3e1;
        }
        .resource-type-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 35px;
            cursor: pointer;
            &.active {
                color: #e1bb82;
            }
            &:nth-child(6) {
                margin-left: 400px;
            }
        }
    }
    .resource-list-container {
        position: relative;
        .resource-buildings {
            height: 100px;
            border-radius: 4px;
            background: rgba(225, 187, 130, 0.1);
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            .building-item {
                flex: 1;
                height: 100%;
                text-align: center;
                cursor: pointer;
                font-size: 36px;
                color: #e1bb82;
                line-height: 80px;
                &.active {
                    border-radius: 4px;
                    background: #e1bb82;
                    color: #5f0d0b;
                }
            }
        }
        .resource-list {
            .resource-item {
                height: 100%;
                color: #fff;
                text-align: center;
                .resource-item-cover {
                    height: 200px;
                    background: url('@/assets/images/demo/cover_3.png') center center / 100% auto
                        no-repeat;
                }
                .resource-item-name {
                    margin-top: 20px;
                    font-size: 30px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
            }
        }
        .swiper-next-icon {
            right: -110px;
            background: url('@/assets/images/common/arrow_right.png') center center / 100% 100%
                no-repeat;
        }
        .swiper-prev-icon {
            left: -110px;
            background: url('@/assets/images/common/arrow_left.png') center center / 100% 100%
                no-repeat;
        }
        .swiper-prev-icon,
        .swiper-next-icon {
            position: absolute;
            cursor: pointer;
            width: 57px;
            height: 55px;
            top: 200px;
            &::after {
                content: '';
            }
        }
    }
}
</style>
