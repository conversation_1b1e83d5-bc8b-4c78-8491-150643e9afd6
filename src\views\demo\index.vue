<template>
    <div class="demo-ui norem">
        <ul class="tab-list">
            <li v-for="item in tabList" :key="item.id" @click="toggleTab(item.id)">
                <el-image :src="item.imgUrl"></el-image>
            </li>
        </ul>
        <SkySetting v-if="activeTab === TAB_ENUM.SKY" />
        <WeatherSetting v-else-if="activeTab === TAB_ENUM.WEATHER" />
    </div>
    <div class="operate-btns-container">
        <div class="operate-btn" @click="resetCamera">复位全景</div>
        <div class="operate-btn" @click="playRouter">播放示例序列</div>
        <div class="operate-btn" @click="stopRouter">停止示例序列</div>
        <div class="operate-btn" @click="addPoint">添加随机点位</div>
        <div class="operate-btn" :class="{ active: activeTool == 'point' }" @click="toolPoint">
            点位标注工具
        </div>
        <div class="operate-btn" @click="getPoints">获取点位工具信息</div>
        <div class="operate-btn" @click="clearPoints">清空点位</div>
    </div>
</template>

<script setup lang="ts">
import SkySetting from './components/skySetting.vue'
import WeatherSetting from './components/weatherSetting.vue'
import skyUrl from '@/assets/images/skySetting.png'
import weatherUrl from '@/assets/images/weatherSetting.png'
import { useDasUE } from '@/hooks/useDasUEHook'
import { ElMessageBox } from 'element-plus'

//tab
enum TAB_ENUM {
    SYSTEM,
    SKY,
    WEATHER,
    SEASON
}

const activeTab = ref<TAB_ENUM>()

const toggleTab = (tabEnum: TAB_ENUM) => {
    return activeTab.value == tabEnum ? (activeTab.value = null) : (activeTab.value = tabEnum)
}

const tabList = [
    { id: TAB_ENUM.SKY, label: '天空设置', imgUrl: skyUrl },
    { id: TAB_ENUM.WEATHER, label: '天气设置', imgUrl: weatherUrl }
]

let ueManager = null
let activeTool = ref('select') // 当前激活的工具
let pointsLayer = ref([]) // 点位图层 pointsLayer

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        // 初始化点击选中工具
        ueManager.dasSelectTool.initSelectTool({
            callback: obj => onBulidingMarkerClick(obj)
        })
    })
})
// 播放序列
const playRouter = async () => {
    await ueManager.dasScene.setSequence('/Game/C_WDS/WDS_Video/Z_Video_S/WDS_Z_LHJD.WDS_Z_LHJD')
    ueManager.dasScene.playSequence()
}
const stopRouter = () => {
    ueManager.dasScene.stopSequence()
}

// 复位
const resetCamera = () => {
    ueManager.dasScene.flyToLocationLLH([111.022581, 32.426525, 1591.3057655902146], {
        pitch: -19.803262832729057,
        yaw: -94.51072392420764,
        roll: -0.015514217815859044
    })
}

// 添加点位（屏幕位置200-900间取点）
const addPoint = async () => {
    clearPoints()
    let posLLH_1 = await ueManager.dasScene.screenToWorld(
        Math.floor(Math.random() * (900 - 200 + 1)) + 200,
        Math.floor(Math.random() * (900 - 200 + 1)) + 200
    )
    let posLLH_2 = await ueManager.dasScene.screenToWorld(
        Math.floor(Math.random() * (900 - 200 + 1)) + 200,
        Math.floor(Math.random() * (900 - 200 + 1)) + 200
    )
    // 目前sdk限制每个uePointsLayer图层的图标只能用同一张图片，若要用不同的图标则需要建多个图层
    const buildingData = [
        {
            id: 1,
            name: '太和宫点位',
            points: [posLLH_1],
            texturePath: 'D:/workfiles/UEWEB/图标/大屏版/taihegong.png',
            pointSize: [65, 192],
            enableDepthTest: false
        },
        {
            id: 2,
            name: '遇真宫点位',
            points: [posLLH_2],
            texturePath: 'D:/workfiles/UEWEB/图标/大屏版/yuzhengong_2.png',
            pointSize: [65, 192],
            enableDepthTest: false
        }
    ]
    // 单个创建点位图层 createInstance
    // Promise.all(buildingData.map(item => ueManager.dasPointsLayer.createInstance(item))).then(
    //     layers => {
    //         pointsLayer.value = layers.map((layer, index) => {
    //             layer.customData = buildingData[index]
    //             return layer
    //         })
    //         console.log('多点位图层创建成功', pointsLayer.value)
    //     }
    // )

    // 批量添加点位图层接口 batchCreateInstance
    const layers = await ueManager.dasPointsLayer.batchCreateInstance(buildingData)
    pointsLayer.value = layers.map((layer, index) => {
        layer.customData = buildingData[index]
        return layer
    })
}

// 标注点击事件
const onBulidingMarkerClick = obj => {
    const item = obj.message ? JSON.parse(obj.message) : {}
    console.log('点击了点位', item)
    if (item.selectLayer?.class == 'DasPointsLayer') {
        pointsLayer.value.forEach(layer => {
            if (layer.id == item.selectLayer.id) {
                ElMessageBox.alert('点击了点位：' + layer.customData.name, '标注点击事件')
            }
        })
    }
}

// 点位标注工具
const toolPoint = async () => {
    if (activeTool.value == 'point') {
        activeTool.value = 'select'
        ueManager.dasPointsTool.finishTool()
        ueManager.dasSelectTool.setToCurrent()
    } else {
        activeTool.value = 'point'
        let param = {
            texturePath: 'D:/workfiles/UEWEB/图标/大屏版/locate.png',
            pointSize: [50, 50]
        }
        ueManager.dasSelectTool.finishTool()
        ueManager.dasPointsTool.setToCurrent()
        ueManager.dasPointsTool.updateAll(param)
    }
}

const getPoints = async () => {
    const info = await ueManager.dasPointsTool.getAll()
    console.log('点位标注工具信息', info)
}

// 清空点位
const clearPoints = async () => {
    let rootGroup = await ueManager.dasGroupLayer.getRoot()
    rootGroup.removeAllLayer()
    pointsLayer.value = []
}

onBeforeUnmount(async () => {
    ueManager?.clearAllLayerAndEvent()
})
</script>

<style scoped lang="scss">
.set_btn {
    width: 36px;
    height: 36px;
    position: absolute;
    top: 62px;
    right: 45px;
    background-image: url('/src/assets/images/sysSetting.png');
    background-size: 100% 100%;
    cursor: pointer;
    z-index: 1;
}
.tab-list {
    position: absolute;
    list-style-type: none;
    top: 45px;
    right: 40px;
    z-index: 1;

    div {
        width: 46px;
        height: 152px;
        cursor: pointer;
        background-size: 100% 100%;
    }
}
.operate-btns-container {
    position: absolute;
    z-index: 999;
    left: 20px;
    top: 20px;
    .operate-btn {
        padding: 10px;
        background: rgba(255, 255, 255, 0.7);
        color: #333;
        border-radius: 5px;
        cursor: pointer;
        margin-bottom: 10px;
        text-align: center;
        &.active {
            background: rgba(255, 255, 255, 1);
            color: #000;
        }
    }
}
</style>
