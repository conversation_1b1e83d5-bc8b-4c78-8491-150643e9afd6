<template>
    <div class="control-tool-bar">
        <img class="control-icon" src="@/assets/images/tool/icon_setting.png" />
        <img class="control-icon" src="@/assets/images/tool/icon_reset.png" @click="onResetView" />
        <img class="control-icon" src="@/assets/images/tool/icon_voice.png" />
        <img
            class="control-icon"
            style="display: none"
            src="@/assets/images/tool/icon_setting.png"
            @click="getCameraInfo"
        />
    </div>
</template>

<script setup lang="ts">
import { useDasUE } from '@/hooks/useDasUEHook'
let ueManager = null
onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
    })
})
const onResetView = () => {
    ueManager.resetCamera()
}
// 获取相机信息
const getCameraInfo = async () => {
    const info = await ueManager.dasScene.getCameraFlyInfoLLH()
    console.log('info', info)
}
</script>

<style lang="scss" scoped>
.control-tool-bar {
    display: flex;
    align-items: center;

    .control-icon {
        width: 60px;
        height: 60px;
        margin-right: 50px;
        cursor: pointer;
        &:hover {
            filter: brightness(1.2);
        }
    }
}
</style>
