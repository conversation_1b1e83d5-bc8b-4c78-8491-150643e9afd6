<template>
    <div class="assistant-tool-bar">
        <img
            class="assistant-character"
            src="@/assets/images/tool/assistant_character.png"
            @click="togglePanel"
        />
        <div v-if="!isShowPanel" class="assistant-tip" @click="togglePanel">
            我是AI智能助手“太和”。点击此处，可以向我提问~
        </div>
        <div v-if="isShowPanel" class="assistant-panel common-modal-border">
            <div class="assistant-info-area">
                <div>
                    <div class="assistant-info-title">
                        <div class="assistant-info-name">你好！</div>
                        <div>我是你的虚拟导游“逍遥”，很高心为您提供便捷服务。</div>
                    </div>
                    <div class="assistant-info-lead">
                        <div class="info-lead-first">
                            <img
                                class="info-lead-icon"
                                src="@/assets/images/tool/icon_questions.png"
                            />
                            <div>
                                <div class="info-lead-title-1">实时问答</div>
                                <div class="info-lead-title-2">知无不言，言无不尽~</div>
                            </div>
                            <div class="info-lead-btn">快速提问</div>
                        </div>
                        <div class="info-lead-route">
                            <img class="info-lead-icon" src="@/assets/images/tool/icon_route.png" />
                            <div class="info-lead-title-1">推荐路线</div>
                            <div class="info-lead-title-2">为您推荐合适的游览路线</div>
                        </div>
                        <div class="info-lead-route">
                            <img
                                class="info-lead-icon"
                                src="@/assets/images/tool/icon_locate.png"
                            />
                            <div class="info-lead-title-1">快速游览</div>
                            <div class="info-lead-title-2">一键“飞到”目的地！</div>
                        </div>
                    </div>
                </div>
                <div class="chat-content">
                    <div v-for="(item, index) in chatList" :key="index" class="chat-item">
                        <div v-if="item.isUser" class="chat-user">
                            <div class="chat-text">{{ item.content }}</div>
                            <div class="chat-user-avatar"></div>
                        </div>
                        <div v-else class="chat-assistant">
                            <div class="chat-user-avatar"></div>
                            <div class="chat-text">{{ item.content }}</div>
                            <div class="chat-graph" @click="isShowGraph = true">关系图谱</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="assistant-input-area">
                <el-input
                    class="assistant-input"
                    v-model="inputVal"
                    type="textarea"
                    placeholder="请输入您想咨询的问题..."
                    :rows="3"
                />
                <div class="assistant-input-btn" @click="onSendMessage">
                    <img class="info-lead-icon" src="@/assets/images/tool/icon_send.png" />
                    <span>发送</span>
                </div>
            </div>
        </div>
        <div class="graph-panel" v-if="isShowGraph">
            <AiAssistant @close="isShowGraph = false" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import AiAssistant from '@/components/tools/AiGraphPanel.vue'
let isShowPanel = ref(false)
let isShowGraph = ref(false)
let inputVal = ref('')
let chatList = ref([])
const togglePanel = () => {
    isShowPanel.value = !isShowPanel.value
}
const onSendMessage = () => {
    if (!inputVal.value.trim()) {
        return ElMessage.warning('请输入内容！')
    }
    chatList.value.push(
        {
            isUser: true,
            content: inputVal.value
        },
        {
            isUser: false,
            content:
                '武当山，中国道教圣地，又名太和山、谢罗山、参上山、仙室山，古有“太岳”、“玄岳”、“大岳”之称。位于湖北省西北部十堰市丹江口市。东接襄阳市，西靠十堰市 ，南望神农架，北临南水北调中线源头丹江口水库。'
        }
    )
    inputVal.value = ''
    scrollToBottom()
}

const scrollToBottom = () => {
    nextTick(() => {
        const chatContent = document.querySelector('.assistant-info-area')
        chatContent!.scrollTo({ top: chatContent?.scrollHeight || 0, behavior: 'smooth' })
    })
}
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
.assistant-tool-bar {
    position: absolute;
    z-index: 1500;
    left: 160px;
    bottom: 150px;
    .assistant-character {
        width: 378px;
        height: 494px;
        cursor: pointer;
    }
    .assistant-tip {
        position: absolute;
        left: 386px;
        bottom: 364px;
        width: 528px;
        height: 256px;
        background: url('@/assets/images/tool/assistant_tip_bg.png') center center / 100% no-repeat;
        font-size: 40px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        text-align: justify;
        padding: 0 60px;
    }
    .assistant-panel {
        position: absolute;
        overflow: hidden;
        padding: 30px 46px 40px 46px;
        left: 372px;
        bottom: 274px;
        width: 750px;
        height: 1100px;
        border-radius: 14px;
        backdrop-filter: blur(14px);
        background: #21201dcc;
        &::before {
            border-radius: 14px;
        }
        .assistant-info-area {
            width: 100%;
            height: 860px;
            margin-bottom: 30px;
            overflow-y: auto;
            .assistant-info-name {
                font-size: 40px;
                color: #e1bb82;
                font-weight: 600;
            }
            .assistant-info-title {
                font-size: 24px;
                color: #e1bb82;
                font-weight: 400;
            }
            .assistant-info-lead {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                color: #fff;
                margin-top: 30px;
                font-family: '';
                .info-lead-title-1 {
                    font-size: 31px;
                    margin-bottom: 4px;
                }
                .info-lead-title-2 {
                    font-size: 24px;
                }
                .info-lead-first {
                    width: 100%;
                    height: 138px;
                    padding: 20px 22px;
                    margin-bottom: 20px;
                    background: #363530e5;
                    border-radius: 14px;
                    display: flex;
                    align-items: center;
                    .info-lead-icon {
                        width: 69px;
                        height: 69px;
                        margin-right: 20px;
                    }
                    .info-lead-btn {
                        background: linear-gradient(180deg, #f7edd2 0%, #dfc193 100%);
                        color: #4a300f;
                        border-radius: 35px;
                        font-size: 24px;
                        padding: 5px 30px;
                        font-family: 'Source Han Serif CN';
                        margin-left: auto;
                        cursor: pointer;
                    }
                }
                .info-lead-route {
                    width: 316px;
                    height: 190px;
                    background: #363530e5;
                    border-radius: 14px;
                    padding: 20px 22px;
                    .info-lead-icon {
                        width: 60px;
                        height: 55px;
                    }
                }
            }
        }
        .chat-content {
            .chat-item {
                font-size: 24px;
                color: #fff;
                margin-top: 30px;
                .chat-user-avatar {
                    width: 112px;
                    height: 104px;
                    background: url('@/assets/images/tool/icon_avatar.png') center center / 100%
                        no-repeat;
                }
                .chat-text {
                    width: 450px;
                    margin-top: 20px;
                    background: #363530e5;
                    border-radius: 14px;
                    padding: 20px;
                    margin-left: 20px;
                }
                .chat-user {
                    display: flex;
                    justify-content: flex-end;
                    .chat-text {
                        border: 1px solid #e1bb82;
                        color: #e1bb82;
                        margin-right: 20px;
                    }
                }
                .chat-assistant {
                    display: flex;
                    justify-content: flex-start;
                    position: relative;
                    padding-bottom: 50px;
                    .chat-graph {
                        position: absolute;
                        bottom: 0;
                        left: 130px;
                        color: #e1bb82;
                        padding: 6px 25px;
                        border-radius: 10px;
                        background: rgba(54, 53, 48, 0.9);
                        cursor: pointer;
                        font-size: 18px;
                    }
                }
            }
        }
        .assistant-input-area {
            width: 100%;
            height: 130px;
            position: relative;
            :deep(.assistant-input) {
                width: 100%;
                height: 100%;
                background: #ede2d2;
                border-radius: 14px;
                font-size: 20px;
                font-weight: 400;
                overflow: hidden;
                .el-textarea__inner {
                    height: 100%;
                    background-color: transparent;
                    box-shadow: none;
                    color: #4a300f;
                }
            }
            .assistant-input-btn {
                width: 146px;
                height: 46px;
                position: absolute;
                right: 20px;
                bottom: 20px;
                padding-right: 15px;
                background: #8c773f;
                color: #fff;
                font-size: 24px;
                border-radius: 35px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                img {
                    width: 33px;
                    height: 31px;
                    margin-right: 10px;
                }
            }
        }
    }
    .graph-panel {
        position: absolute;
        left: 1150px;
        bottom: 350px;
    }
}
</style>
