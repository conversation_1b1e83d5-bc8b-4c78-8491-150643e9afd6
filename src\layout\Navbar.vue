<template>
    <div class="menu-bar">
        <div
            class="menu-item menu-item-home"
            :class="{ active: currentRoute === '/home' }"
            @click="goPage('home')"
        ></div>
        <div
            class="menu-item menu-item-resource"
            :class="{ active: currentRoute === '/resource' }"
            @click="goPage('resource')"
        ></div>
        <div
            class="menu-item menu-item-application"
            :class="{ active: currentRoute === '/application' }"
            @click="goPage('application')"
        ></div>
    </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { computed } from 'vue'
import { useNavStore } from '@/stores/nav'
const router = useRouter()

const currentRoute = computed(() => {
    return router.currentRoute.value.fullPath
})
const goPage = (type: string) => {
    useNavStore().setMenuType(type)
    const path = `/${type}`
    if (currentRoute.value === path) return
    router.push(path)
}
</script>

<style lang="scss" scoped>
.menu-bar {
    position: fixed;
    left: 150px;
    top: 50%;
    z-index: 1001;
    transform: translateY(-55%);
    .menu-item {
        width: 200px;
        height: 200px;
        margin-bottom: 110px;
        cursor: pointer;
        &.active {
            width: 715px;
        }
    }
    .menu-item-home {
        background: url('@/assets/images/tool/menu_home.png') center center / 100% no-repeat;
        &.active {
            background: url('@/assets/images/tool/menu_home_active.png') center center / 100%
                no-repeat;
        }
    }
    .menu-item-resource {
        background: url('@/assets/images/tool/menu_resource.png') center center / 100% no-repeat;
        &.active {
            background: url('@/assets/images/tool/menu_resource_active.png') center center / 100%
                no-repeat;
        }
    }
    .menu-item-application {
        background: url('@/assets/images/tool/menu_application.png') center center / 100% no-repeat;
        &.active {
            background: url('@/assets/images/tool/menu_application_active.png') center center / 100%
                no-repeat;
        }
    }
}
</style>
