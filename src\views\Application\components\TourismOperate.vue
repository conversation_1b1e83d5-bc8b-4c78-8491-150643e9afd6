<template>
    <div class="tourism-panel-box right-panel-content">
        <div class="info-content-title">
            <span class="info-content-title-text">基础设施</span>
        </div>
        <div class="infrastucture-content">
            <div class="infrastucture-item" v-for="(item, index) in infrastuctureList" :key="index">
                <div class="infrastucture-inner">
                    <div class="infrastucture-icon" :class="'icon-' + item.key"></div>
                    <div class="infrastucture-text">
                        <div class="infrastucture-label">{{ item.name }}</div>
                        <img class="infrastucture-hr" src="@/assets/images/resource/hr_line.png" />
                        <div>
                            <span class="infrastucture-value">{{ item.value }}</span>
                            <span class="infrastucture-unit">{{ item.unit }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">文旅创新</span>
        </div>
        <div class="theme-tour-content">
            <div class="theme-tour-item">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>主题导览</span>
                </div>
                <div class="theme-list">
                    <div class="theme-card" v-for="(item, index) in tourList" :key="index">
                        <div class="theme-card-name">{{ item.name }}</div>
                        <div class="theme-card-desc">{{ item.desc }}</div>
                    </div>
                </div>
            </div>
            <div class="theme-tour-item">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>数字文创</span>
                </div>
                <div class="product-list">
                    <div class="product-item" v-for="(item, index) in relicProduct" :key="index">
                        <div class="product-icon" :class="'icon-' + item.key"></div>
                        <div class="product-info">
                            <div class="product-title">{{ item.name }}</div>
                            <div>
                                <span class="product-value">{{ item.value }}</span>
                                <span class="product-unit">{{ item.unit }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="relic-product">
            <div class="info-small-subtitle">
                <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                <span>新文创场景</span>
            </div>
            <div class="scenario-content">
                <div class="scenario-nav">
                    <div class="nav-item active">新文创新场景</div>
                </div>
                <div class="scenario-list">
                    <div class="nav-arrow">
                        <img src="@/assets/images/common/icon_up.png" alt="" />
                    </div>
                    <div class="nav-list">
                        <div class="nav-item" v-for="(item, index) in scenarioList" :key="index">
                            {{ item.name }}
                        </div>
                    </div>
                    <div class="nav-arrow">
                        <img src="@/assets/images/common/icon_down.png" alt="" />
                    </div>
                    <img
                        class="select-icon"
                        src="@/assets/images/application/icon_select.png"
                        alt=""
                    />
                </div>

                <div class="scenario-main">
                    <div class="scenario-desc">
                        <div class="scenario-cover"></div>
                        <div>
                            武当一梦，古朴5.4万平方米的超级文旅综合体，集合了7项浸式演艺，数字文体验，非遗文化大师工坊等。
                        </div>
                    </div>
                    <div class="scenario-stats">
                        <div class="stats-row">
                            <div class="stats-item">
                                <div class="stats-label">昨日接待游客</div>
                                <div class="stats-value">312<span class="stats-unit">人</span></div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">累计接待游客</div>
                                <div class="stats-value">
                                    312<span class="stats-unit">万元</span>
                                </div>
                            </div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-item">
                                <div class="stats-label">累计营收</div>
                                <div class="stats-value">
                                    312<span class="stats-unit">万元</span>
                                </div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">获得奖项</div>
                                <div class="stats-value award">缪斯设计铂金奖</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const infrastuctureList = ref([
    { name: '大巴车', value: '100', unit: '个', key: 'bus' },
    { name: '停车场', value: '100', unit: '个', key: 'parking' },
    { name: '服务中心', value: '100', unit: '个', key: 'service' },
    { name: '餐厅', value: '100', unit: '个', key: 'restaurant' },
    { name: '缆车', value: '100', unit: '个', key: 'cable' },
    { name: '5G基站', value: '100', unit: '个', key: '5g' }
])
const tourList = ref([
    { name: '大岳武当', desc: '全国首个山岳文化主题叙事型漫游' },
    { name: '古建艺术', desc: '全国首个山岳文化主题叙事型漫游' },
    { name: '大岳武当', desc: '全国首个山岳文化主题叙事型漫游' },
    { name: '古建艺术', desc: '全国首个山岳文化主题叙事型漫游' }
])
const relicProduct = ref([
    { name: '数字藏品销量', value: '100', unit: '个', key: '1' },
    { name: '文化素材交易', value: '100', unit: '个', key: '2' },
    { name: 'IP授权合作', value: '100', unit: '个', key: '3' },
    { name: '文创产品营收', value: '100', unit: '个', key: '4' }
])
const scenarioList = ref([
    { name: '大岳武当' },
    { name: '太极之夜' },
    { name: '武当一梦' },
    { name: 'VR大空间' },
    { name: '太极之夜' }
])
</script>

<style lang="scss" scoped>
.tourism-panel-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: left;
    &.right-panel-content .info-content-title {
        margin-top: 0px;
    }
    .info-small-subtitle {
        justify-content: flex-end;
    }
    .infrastucture-content {
        font-family: 'Source Han Sans CN';
        width: 1400px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-bottom: 40px;
        .infrastucture-item {
            display: inline-block;
            margin-top: 40px;
            width: 427px;
            height: 172px;
            background: linear-gradient(
                90deg,
                rgba(167, 148, 90, 0.06) 0%,
                rgba(189, 168, 104, 0.3) 100%
            );
        }
        .infrastucture-inner {
            width: 100%;
            height: 100%;
            padding: 0 32px;
            display: flex;
            align-items: center;
            background: url('@/assets/images/application/bg_border.png') center center / 100%
                no-repeat;
            color: #fff;
            .infrastucture-icon {
                width: 125px;
                height: 125px;
                margin-right: 20px;
                &.icon-bus {
                    background: url('@/assets/images/application/icon_bus.png') center center / 100%
                        100% no-repeat;
                }
                &.icon-parking {
                    background: url('@/assets/images/application/icon_parking.png') center center /
                        100% 100% no-repeat;
                }
                &.icon-service {
                    background: url('@/assets/images/application/icon_service.png') center center/
                        100% 100% no-repeat;
                }
                &.icon-restaurant {
                    background: url('@/assets/images/application/icon_restaurant.png') center
                        center/ 100% 100% no-repeat;
                }
                &.icon-cable {
                    background: url('@/assets/images/application/icon_cable.png') center center/
                        100% 100% no-repeat;
                }
                &.icon-5g {
                    background: url('@/assets/images/application/icon_5g.png') center center/ 100%
                        100% no-repeat;
                }
            }
            .infrastucture-text {
                text-align: left;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            .infrastucture-label {
                font-size: 30px;
            }
            .infrastucture-hr {
                width: 204.621px;
                height: 3px;
                margin: 15px 0 5px 0;
            }
            .infrastucture-value {
                font-size: 50px;
                font-weight: 600;
            }
            .infrastucture-unit {
                font-size: 32px;
                margin-left: 10px;
            }
        }
    }
    .theme-tour-content {
        display: flex;
        width: 1657px;
        font-family: 'Source Han Sans CN';
        color: #fff;
        .theme-tour-item {
            flex: 1;
            .theme-list {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                .theme-card {
                    position: relative;
                    width: 48%;
                    height: 200px;
                    border-radius: 10px;
                    margin-top: 40px;
                    background: url('@/assets/images/demo/cover_3.png') center center/ 100% 100%
                        no-repeat;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    overflow: hidden;
                    .theme-card-name {
                        width: 100%;
                        position: absolute;
                        bottom: 0px;
                        left: 0px;
                        font-size: 30px;
                        height: 48px;
                        background: rgba(0, 0, 0, 0.8);
                        padding-left: 20px;
                    }
                    .theme-card-desc {
                        height: 100%;
                        font-size: 24px;
                        text-align: center;
                        padding: 48px;
                        background: #00000066;
                        display: none;
                    }
                    &:hover {
                        border: 1px solid #e1bb82;
                        .theme-card-desc {
                            display: block;
                        }
                    }
                }
            }
        }
        .product-list {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .product-item {
                display: flex;
                align-items: center;
                position: relative;
                width: 48%;
                height: 200px;
                margin-top: 40px;
                color: #fff;
                .product-icon {
                    width: 150px;
                    height: 150px;
                    margin: 0 auto;
                    &.icon-1 {
                        background: url('@/assets/images/application/icon_culture_1.png') center
                            center/ 100% 100% no-repeat;
                    }
                    &.icon-2 {
                        background: url('@/assets/images/application/icon_culture_2.png') center
                            center/ 100% 100% no-repeat;
                    }
                    &.icon-3 {
                        background: url('@/assets/images/application/icon_culture_3.png') center
                            center/ 100% 100% no-repeat;
                    }
                    &.icon-4 {
                        background: url('@/assets/images/application/icon_culture_4.png') center
                            center/ 100% 100% no-repeat;
                    }
                }
                .product-title {
                    color: #e1bb82;
                    font-size: 30px;
                }
                .product-value {
                    font-size: 50px;
                    font-weight: 600;
                }
                .product-unit {
                    font-size: 32px;
                    margin-left: 10px;
                }
            }
        }
    }
    .relic-product {
        width: 1657px;
        margin-top: 30px;

        .scenario-content {
            display: flex;
            align-items: center;
            margin-top: 20px;
            height: 467px;

            .scenario-nav {
                width: 111px;
                height: 390px;
                background: url('@/assets/images/application/bg_info_1.png') center center / 100%
                    100% no-repeat;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .nav-item {
                    writing-mode: vertical-lr;
                    text-orientation: upright;
                    font-size: 32px;
                    font-weight: 600;
                    letter-spacing: 8px;
                    color: #fff;
                }
            }
            .scenario-list {
                position: relative;
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                font-size: 40px;
                padding-right: 60px;
                .nav-list {
                    display: flex;
                    flex-direction: column;
                    gap: 25px;
                    margin: 25px 0;

                    .nav-item {
                        font-size: 40px;
                        letter-spacing: 4px;
                        text-align: center;
                        color: #ffe1b6;
                        &:nth-child(1),
                        &:nth-child(5) {
                            font-size: 20px;
                            color: #fff;
                        }
                        &:nth-child(2),
                        &:nth-child(4) {
                            font-size: 30px;
                            color: #fff;
                        }
                    }
                }

                .select-icon {
                    position: absolute;
                    width: 60px;
                    height: 60px;
                    right: 30px;
                }

                .nav-arrow {
                    width: 53.18px;
                    height: 57.665px;
                    cursor: pointer;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .scenario-main {
                width: 1198px;
                background: url('@/assets/images/application/bg_info_2.png') center center / 100%
                    100% no-repeat;
                color: #ffffff;
                padding: 40px 86px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .scenario-desc {
                    width: 451px;
                    font-size: 24px;
                    line-height: 1.6;
                    color: #ffffff;
                    .scenario-cover {
                        width: 100%;
                        height: 245px;
                        background: url('@/assets/images/demo/cover_4.png') center center / 100%
                            100% no-repeat;
                        margin-bottom: 15px;
                    }
                }

                .scenario-stats {
                    display: flex;
                    flex-direction: column;
                    gap: 30px;

                    .stats-row {
                        display: flex;
                        gap: 80px;

                        .stats-item {
                            flex: 1;

                            .stats-label {
                                font-size: 30px;
                                color: #ffe2b4;
                                margin-bottom: 10px;
                            }

                            .stats-value {
                                font-size: 50px;
                                font-weight: 600;
                                color: #ffffff;

                                .stats-unit {
                                    font-size: 32px;
                                    margin-left: 8px;
                                }

                                &.award {
                                    font-size: 40px;
                                    color: #ffffff;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
