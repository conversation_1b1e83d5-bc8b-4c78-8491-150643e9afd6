<template>
    <div class="relic-protect-box right-panel-content">
        <div class="info-content-title">
            <span class="info-content-title-text">文保监测</span>
        </div>
        <div class="info-monitor-data">
            <div class="info-monitor-item">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>古建筑病害监测</span>
                </div>
                <div class="info-totals">
                    <div class="total-item" v-for="(item, index) in buildingDatas" :key="index">
                        <div class="total-circle">
                            <div class="total-value">
                                <span class="total-number">{{ item.value }}</span>
                                <span class="total-unit">{{ item.unit }}</span>
                            </div>
                        </div>
                        <div class="total-label">{{ item.label }}</div>
                    </div>
                </div>
            </div>
            <div class="info-monitor-item">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>生态安全监测</span>
                </div>
                <div class="info-totals">
                    <div class="total-item" v-for="(item, index) in ecologyDatas" :key="index">
                        <div class="total-circle">
                            <div class="total-value">
                                <span class="total-number">{{ item.value }}</span>
                                <span class="total-unit">{{ item.unit }}</span>
                            </div>
                        </div>
                        <div class="total-label">{{ item.label }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="monitor-charts">
            <div ref="chartLeftRef" class="chart-box"></div>
            <div ref="chartRightRef" class="chart-box"></div>
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">保护工程</span>
        </div>
        <div class="project-content">
            <div class="project-info-left">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>文物保护工程统计</span>
                </div>
                <div class="project-totals-list">
                    <div class="project-total">
                        <img src="@/assets/images/application/icon_project.png" alt="" />
                        <span>
                            <div>在建工程</div>
                            <div>
                                <span class="total-number">17</span
                                ><span class="total-unit">个</span>
                            </div>
                        </span>
                    </div>
                    <div class="project-total">
                        <img src="@/assets/images/application/icon_project_done.png" alt="" />
                        <span>
                            <div>完工工程</div>
                            <div>
                                <span class="total-number">17</span
                                ><span class="total-unit">个</span>
                            </div>
                        </span>
                    </div>
                </div>
                <div ref="chartProjectRef" class="chart-box"></div>
            </div>
            <div class="project-info-right">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>重大文物保护工程</span>
                </div>
                <div class="project-timeline">
                    <div class="timeline-container">
                        <div class="timeline-line"></div>
                        <div
                            v-for="(item, index) in projectList"
                            :key="index"
                            class="timeline-item"
                        >
                            <div class="timeline-dot">
                                <img src="@/assets/images/common/icon_cube.png" alt="" />
                            </div>
                            <div class="timeline-content">
                                <img
                                    class="timeline-cover"
                                    src="@/assets/images/demo/cover_3.png"
                                    alt=""
                                />
                                <div class="timeline-info">
                                    <div class="timeline-time">{{ item.time }}</div>
                                    <div class="timeline-title">{{ item.title }}</div>
                                    <div class="timeline-desc">{{ item.content }}</div>
                                    <div class="timeline-more">了解更多 >></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
let baseWidth = 3840
const chartLeftRef = ref()
const chartRightRef = ref()
const chartProjectRef = ref()
let chartLeftInstance = null
let chartRightInstance = null
let chartProjectInstance = null

const buildingDatas = ref([
    { label: '监测点位数量', value: '104', unit: '个' },
    { label: '监测设备在线率', value: '99.6', unit: '%' }
])

const ecologyDatas = ref([
    { label: '监测点位数量', value: '104', unit: '个' },
    { label: '监测设备在线率', value: '99.6', unit: '%' }
])
const colorList = ['#FF6A3A', '#FFD03B', '#2BA471', '#FFDA98']

const monitorData = ref([
    { name: '表层风化', value: 33.2 },
    { name: '生物病害', value: 33.2 },
    { name: '结构应力', value: 33.2 },
    { name: '木材含水率', value: 33.2 }
])

const ecologyData = ref([
    { name: '水文监测', value: 33.2 },
    { name: '气象监测', value: 33.2 },
    { name: '土壤监测', value: 33.2 }
])

const protectData = ref([
    { name: '古遗址保护', value: 33.2 },
    { name: '生态景观保护', value: 33.2 },
    { name: '文保修复', value: 33.2 },
    { name: '文保监测', value: 33.2 }
])

const projectList = ref([
    {
        time: '2016年3月',
        title: '遇真宫顶升工程',
        content: '遇真宫顶升工程是为配合南水北调中线工程，将遇真宫整体抬升15米的重大文物保护项目。'
    },
    {
        time: '2016年3月',
        title: '古建彩绘多光谱修复',
        content: '复原明代彩绘3800㎡\n建立颜料数据库（123类颜料科学谱档案）'
    },
    {
        time: '2016年3月',
        title: '遇真宫古建筑保护',
        content: '对遇真宫古建筑进行全面的保护和修缮工作，确保文物的完整性和安全性。'
    }
])

onMounted(() => {
    nextTick(() => {
        chartLeftInstance = initChart(chartLeftRef, '古建监测', monitorData.value)
        chartRightInstance = initChart(chartRightRef, '生态监测', ecologyData.value)
        chartProjectInstance = initChart(chartProjectRef, '保护工程', protectData.value)
    })

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
})

onBeforeUnmount(async () => {
    // 移除事件监听器
    window.removeEventListener('resize', handleResize)
    // 销毁图表实例
    if (chartLeftInstance) {
        chartLeftInstance.dispose()
        chartLeftInstance = null
    }
    if (chartRightInstance) {
        chartRightInstance.dispose()
        chartRightInstance = null
    }
    if (chartProjectInstance) {
        chartProjectInstance.dispose()
        chartProjectInstance = null
    }
})
// 计算响应式字体大小
const getResponsiveFontSize = (baseSize: number) => {
    const screenWidth = window.innerWidth
    const scale = screenWidth / baseWidth
    return Math.max(Math.round(baseSize * scale), 10) // 最小字体10px
}

// 防抖函数
const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout
    return function executedFunction(...args: any[]) {
        const later = () => {
            clearTimeout(timeout)
            func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
    }
}

// 处理窗口大小变化
const handleResize = debounce(() => {
    if (chartLeftInstance) {
        chartLeftInstance.dispose()
        chartLeftInstance = initChart(chartLeftRef, '古建监测', monitorData.value)
    }
    if (chartRightInstance) {
        chartRightInstance.dispose()
        chartRightInstance = initChart(chartRightRef, '生态监测', ecologyData.value)
    }
    if (chartProjectInstance) {
        chartProjectInstance.dispose()
        chartProjectInstance = initChart(chartRightRef, '保护工程', protectData.value)
    }
}, 300)

// 通用图表初始化方法
const initChart = (chartRef: any, title: string, data: any[]) => {
    if (!chartRef.value) return null
    chartRef.value?.removeAttribute('_echarts_instance_')
    const instance = echarts.init(chartRef.value)
    const totalNum = data.reduce((acc, item) => acc + item.value, 0)
    const option = {
        backgroundColor: 'transparent',
        title: {
            text: title,
            left: '20%',
            top: '43%',
            textAlign: 'center',
            textStyle: {
                color: '#E1BB82',
                fontSize: getResponsiveFontSize(14),
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(225, 187, 130, 0.4)',
            borderColor: '#EEC173',
            padding: 20,
            textStyle: {
                fontSize: getResponsiveFontSize(20),
                color: '#ffffff'
            }
        },
        legend: {
            orient: 'vertical',
            right: '0%',
            top: 'center',
            itemWidth: Math.max(Math.round(5 * (window.innerWidth / baseWidth)), 3),
            itemHeight: Math.max(Math.round(5 * (window.innerWidth / baseWidth)), 3),
            itemGap: Math.max(Math.round(12 * (window.innerWidth / baseWidth)), 8),
            icon: 'circle',
            textStyle: {
                rich: {
                    name: {
                        color: '#FFE2B4',
                        fontSize: getResponsiveFontSize(12),
                        width: Math.max(Math.round(80 * (window.innerWidth / baseWidth)), 30)
                    },
                    value: {
                        color: '#fff',
                        fontSize: getResponsiveFontSize(14),
                        fontWeight: 'bold',
                        width: Math.max(Math.round(50 * (window.innerWidth / baseWidth)), 30)
                    }
                }
            },
            formatter: name => {
                let obj = data.find(item => item.name === name)
                let value = obj?.value || 0
                let percent = totalNum === 0 ? '0%' : ((value / totalNum) * 100).toFixed(1) + '%'
                return `{name|${name}}{value|${percent}}`
            }
        },
        series: [
            {
                type: 'pie',
                radius: ['60%', '70%'],
                center: ['22%', '50%'],
                avoidLabelOverlap: false,
                startAngle: 90,
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                emphasis: {
                    scale: true,
                    scaleSize: 4
                },
                data: data.map((item: { name: string; value: number }, index) => ({
                    name: item.name,
                    value: item.value,
                    itemStyle: {
                        color: colorList[index % colorList.length],
                        borderColor: 'rgba(0, 0, 0, 0.2)',
                        borderWidth: 3
                    }
                }))
            },
            // 内边框的设置
            {
                type: 'pie',
                radius: ['50%', '53%'],
                center: ['22%', '50%'],
                tooltip: {
                    show: false
                },
                emphasis: {
                    scale: false
                },
                labelLine: {
                    show: false
                },
                data: [0],
                itemStyle: {
                    color: '#F9C68D'
                }
            }
        ]
    }
    instance.setOption(option)
    return instance
}
</script>

<style lang="scss" scoped>
.relic-protect-box {
    height: 100%;
    text-align: right;

    &.right-panel-content .info-content-title {
        margin-top: 0px;
    }
    .info-small-subtitle {
        justify-content: flex-end;
        margin-bottom: 20px;
    }
    .info-monitor-data {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .info-totals {
            padding-right: 50px;
        }
    }
    .monitor-charts {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
    .chart-box {
        height: 380px;
        width: 790px;
        margin-left: 55px;
    }
    .project-content {
        display: flex;
        align-items: flex-start;
        .project-info-left {
            width: 800px;
            .project-totals-list {
                margin-top: 100px;
                display: flex;
                color: #ffe2b4;
                font-size: 30px;
                .project-total {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                    img {
                        width: 134px;
                        height: 126px;
                    }
                    .total-number {
                        color: #fff;
                        font-size: 50px;
                        font-weight: 600;
                    }
                    .total-unit {
                        font-size: 32px;
                    }
                }
            }
            .chart-box {
                width: 100%;
            }
        }
        .project-info-right {
            width: calc(100% - 900px);
            margin-left: 100px;

            .project-timeline {
                margin-top: 40px;

                .timeline-container {
                    position: relative;
                    padding: 20px 0;

                    .timeline-line {
                        position: absolute;
                        left: 337px;
                        top: 0;
                        bottom: 0;
                        width: 2px;
                        background: linear-gradient(to bottom, #ffe2b4, #e1bb82);
                        z-index: 1;
                    }

                    .timeline-item {
                        position: relative;
                        margin-bottom: 80px;
                        display: flex;
                        align-items: flex-start;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .timeline-dot {
                            position: absolute;
                            left: 357px;
                            top: 50px;
                            z-index: 2;
                            width: 24px;
                            height: 24px;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            img {
                                width: 20px;
                                height: 20px;
                            }
                        }

                        .timeline-content {
                            width: 100%;
                            display: flex;
                            align-items: flex-start;
                            gap: 40px;

                            .timeline-cover {
                                width: 337px;
                                height: 204px;
                                object-fit: cover;
                                border-radius: 8px;
                                flex-shrink: 0;
                            }

                            .timeline-info {
                                flex: 1;
                                padding-top: 10px;

                                .timeline-time {
                                    color: #ffe2b4;
                                    font-size: 34px;
                                    font-weight: 600;
                                    margin-bottom: 20px;
                                }

                                .timeline-title {
                                    color: #ffe2b4;
                                    font-size: 30px;
                                    font-weight: 600;
                                    margin-bottom: 15px;
                                    line-height: 1.2;
                                }

                                .timeline-desc {
                                    color: #ffffff;
                                    font-size: 24px;
                                    line-height: 1.5;
                                    margin-bottom: 20px;
                                    white-space: pre-line;
                                }

                                .timeline-more {
                                    color: #ffe2b4;
                                    font-size: 20px;
                                    cursor: pointer;
                                    transition: color 0.3s ease;

                                    &:hover {
                                        color: #ffffff;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
