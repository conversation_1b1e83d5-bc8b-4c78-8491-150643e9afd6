<template>
    <div class="relic-protect-box right-panel-content">
        <div class="info-content-title">
            <span class="info-content-title-text">文保监测</span>
        </div>
        <div class="info-monitor-data">
            <div class="info-monitor-item">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>古建筑病害监测</span>
                </div>
                <div class="info-totals">
                    <div class="total-item" v-for="(item, index) in buildingDatas" :key="index">
                        <div class="total-circle">
                            <div class="total-value">
                                <span class="total-number">{{ item.value }}</span>
                                <span class="total-unit">{{ item.unit }}</span>
                            </div>
                        </div>
                        <div class="total-label">{{ item.label }}</div>
                    </div>
                </div>
            </div>
            <div class="info-monitor-item">
                <div class="info-small-subtitle">
                    <img src="@/assets/images/common/icon_subtitle.png" alt="" />
                    <span>生态安全监测</span>
                </div>
                <div class="info-totals">
                    <div class="total-item" v-for="(item, index) in ecologyDatas" :key="index">
                        <div class="total-circle">
                            <div class="total-value">
                                <span class="total-number">{{ item.value }}</span>
                                <span class="total-unit">{{ item.unit }}</span>
                            </div>
                        </div>
                        <div class="total-label">{{ item.label }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="monitor-charts">
            <img class="demo-echart-img" src="@/assets/images/demo/echart_2.png" />
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">保护工程</span>
        </div>
        <div class="monitor-info-left"></div>
        <div class="monitor-info-right"></div>
    </div>
</template>

<script setup lang="ts">
const buildingDatas = ref([
    { label: '监测点位数量', value: '104', unit: '个' },
    { label: '监测设备在线率', value: '99.6', unit: '%' }
])

const ecologyDatas = ref([
    { label: '监测点位数量', value: '104', unit: '个' },
    { label: '监测设备在线率', value: '99.6', unit: '%' }
])

onMounted(() => {})
</script>

<style lang="scss" scoped>
.relic-protect-box {
    height: 100%;
    text-align: right;

    &.right-panel-content .info-content-title {
        margin-top: 20px;
    }
    .info-monitor-data {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .info-small-subtitle {
            justify-content: flex-end;
        }
        .info-totals {
            padding-right: 50px;
        }
    }
    .monitor-charts {
        margin: 40px 0;
        .demo-echart-img {
            width: 1500px;
            height: auto;
        }
    }

    .chart-box {
        height: 409px;
        width: 100%;
    }
}
</style>
