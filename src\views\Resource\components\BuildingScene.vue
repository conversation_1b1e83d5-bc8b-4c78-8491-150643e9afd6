<template>
    <div class="building-scene-container">
        <div class="scene-back-btn common-modal-border" @click="closeBuildingScene">
            <img src="@/assets/images/common/icon_back.png" />
            <span>返回全局</span>
        </div>
        <div class="scene-detail-entrance">
            <div class="scene-detail-btn building" @click="toggleDetail(1)">
                <img src="@/assets/images/resource/scene_building.png" />
                <div>建筑风貌</div>
            </div>
            <div class="scene-detail-btn relic" @click="toggleDetail(2)">
                <img src="@/assets/images/resource/scene_relic.png" />
                <div>文物新语</div>
            </div>
            <div class="scene-detail-btn culture" @click="toggleDetail(3)">
                <img src="@/assets/images/resource/scene_culture.png" />
                <div>道脉仙踪</div>
            </div>
        </div>
        <BuildingResource v-if="isShowDetail" :type="activeMenuType" @close="toggleDetail" />
        <ResourceDetail v-if="isShowResourceDetail" @close="toggleResourceDetail" />
        <BuildingComment />
        <InfoModal v-if="isShowInfo" :detail-data="detailInfo" @close="isShowInfo = false" />
    </div>
</template>
<script setup lang="ts">
import ResourceDetail from './ResourceDetail.vue'
import BuildingResource from './BuildingResource.vue'
import BuildingComment from './BuildingComment.vue'
import { useRouter, useRoute } from 'vue-router'
import { useDasUE } from '@/hooks/useDasUEHook'
import { buildingData } from '@/config/buildingData'
import InfoModal from '@/components/commonPanel/InfoModal.vue'

const router = useRouter()
const route = useRoute()
let ueManager = null
let isShowDetail = ref(false)
let isShowResourceDetail = ref(false)
let activeMenuType = ref(1) // 详情类型（1建筑风貌/2文物新语/3道脉仙踪）
let pointLayers = ref([])
let pointsData = ref([])
let isShowInfo = ref(false)

const markerBasePath = window.webConfig.mapMarkerUrl
const htmlBasePath = window.webConfig.htmlResourceUrl
let detailInfo = ref({
    video: `${markerBasePath}/yzg_protect.m4v`,
    title: '遇真宫顶升工程',
    remark: '因南水北调工程，遇真宫面临被淹没的危险。2012年，在原址实施垫高保护工程，将遇真宫的山门、东、西宫门原地顶升15米，创造了世界古建筑整体顶升的纪录。'
})

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.dasSelectTool.initSelectTool({
            onClick: obj => onClickBuildingMarker(obj)
        })
        initPalace()
        addBuildingInfoMarker()
    })
})

// 根据宫观初始化信息
const initPalace = async () => {
    let palaceName = route.query.palace
    if (palaceName === '遇真宫') {
        // ueManager.dasScene.flyToCameraPositionByName('遇真宫') // 后续飞行点位取正确了采用这种方式切换相机
        ueManager.dasScene.unloadLevel('WholeMoutain')
        ueManager.dasScene.loadLevel('YuZhenGong')
        // ueManager.dasScene.flyToCameraPositionByName('遇真宫')
        let cameraPalace: unknown = buildingData.find(item => item.name === '遇真宫').cameraPalace
        ueManager.dasScene.flyToLocationLLH(cameraPalace.locationLLH, cameraPalace.rotationLLH)
        pointsData.value = [
            {
                id: 1,
                name: '遇真宫介绍点位1',
                texturePath: `${markerBasePath}/marker_point.png`,
                points: [[111.11722680503301, 32.505788056227104, 150.09810659316278]],
                pointSize: [50, 50],
                enableDepthTest: false
            },
            {
                id: 2,
                name: '虚拟复原',
                texturePath: `${markerBasePath}/marker_palace.png`,
                points: [[111.118648852503, 32.505485863348184, 159.99990863399762]],
                pointSize: [70, 70],
                enableDepthTest: false
            }
        ]
    } else {
        let cameraPalace: unknown = buildingData.find(item => item.name === '太和宫').cameraPalace
        ueManager.dasScene.flyToLocationLLH(cameraPalace.locationLLH, cameraPalace.rotationLLH)
        pointsData.value = [
            {
                id: 1,
                name: '介绍点位1',
                texturePath: `${markerBasePath}/marker_point.png`,
                points: [[111.00466666828913, 32.4007611323343, 1584.8962630357946]],
                pointSize: [80, 80],
                enableDepthTest: false,
                autoScale: false
            },
            {
                id: 2,
                name: '介绍点位2',
                texturePath: `${markerBasePath}/marker_point.png`,
                points: [[111.00468884759356, 32.400853241784844, 1584.8174447923873]],
                pointSize: [80, 80],
                enableDepthTest: false,
                autoScale: false
            },
            {
                id: 3,
                name: '雷火炼殿',
                texturePath: `${markerBasePath}/marker_thunder.png`,
                points: [[111.00459950658862, 32.400843214297765, 1581.7400791181972]],
                pointSize: [120, 120],
                enableDepthTest: false,
                autoScale: false
            }
        ]
    }
}

// 根据点位列表添加标注+弹窗点位
const addBuildingInfoMarker = async () => {
    // const screenWidth = 3840
    // const ratio = screenWidth / 9600
    // const popupW = 1100
    // const popupH = 550
    const dataPopups = pointsData.value.map(item => {
        return {
            url: `${htmlBasePath}/billboardInteractive.html`,
            posLLH: item.points[0],
            visible: false,
            // width: popupW * ratio + 10,
            // height: popupH * ratio + 10,
            // x: (popupW * ratio + 120) / 4,
            // y: (popupH * ratio - 60) / 2
            width: 450,
            height: 230,
            x: 140,
            y: 80
        }
    })
    const layers = await ueManager.dasPointsLayer.batchCreateInstanceWithPopup({
        dataPoints: pointsData.value,
        dataPopups: dataPopups,
        onMessage: (layer, json) => {
            isShowResourceDetail.value = true
            // let item = pointLayers.value.find(item => item.popupLayer.id == json.id)
            // ElMessageBox.alert('点击了弹窗：' + item.customData.name, '弹窗点击事件')
            console.log('layer, json:', layer, json)
        }
    })
    pointLayers.value = layers.map((layer, index) => {
        layer.customData = pointsData.value[index]
        return layer
    })
}

// 标牌点击事件（标注点位点击后显示详情弹窗）
const onClickBuildingMarker = async obj => {
    const item = obj.message ? JSON.parse(obj.message) : {}
    if (item.selectLayer?.class == 'DasPointsLayer') {
        pointLayers.value.forEach(async layer => {
            if (layer.id == item.selectLayer.id) {
                if (layer.customData.name === '雷火炼殿') {
                    ueManager.dasScene.flyToLocationLLH(
                        [111.00162852745197, 32.39867360661347, 1633.5690529521062],
                        {
                            pitch: -21.393403655251245,
                            yaw: -37.60377551160582,
                            roll: 0
                        },
                        1
                    )
                    setTimeout(() => {
                        playThunder()
                    }, 1000)
                } else if (layer.customData.name === '虚拟复原') {
                    isShowInfo.value = true
                } else {
                    let isVisible = await layer.popupLayer.isVisible()
                    layer.popupLayer.setVisible(!isVisible)
                }
            }
        })
    }
}
// 播放序列
const playThunder = async () => {
    // 播放动画时隐藏标注
    pointLayers.value.forEach(async layer => {
        layer.setVisible(false)
        layer.popupLayer.setVisible(false)
    })
    await ueManager.dasScene.setSequence('/Game/C_WDS/WDS_Video/Z_Video_S/WDS_Z_LHJD.WDS_Z_LHJD')
    ueManager.dasScene.playSequence()
    let res = await ueManager.dasScene.getStatus()
    if (res?.duration) {
        setTimeout(() => {
            // 播放动画结束时显示标注
            pointLayers.value.forEach(async layer => {
                layer.setVisible(true)
            })
        }, res.duration * 1000)
    }
}

const toggleDetail = (type?: number) => {
    isShowDetail.value = !isShowDetail.value
    activeMenuType.value = type || 1
}

const toggleResourceDetail = () => {
    isShowResourceDetail.value = !isShowResourceDetail.value
}

// 返回全景
const closeBuildingScene = async () => {
    ueManager.dasScene.loadLevel('WholeMoutain')
    let palaceName = route.query.palace
    if (palaceName === '遇真宫') {
        ueManager.dasScene.unloadLevel('YuZhenGong')
    }
    await ueManager?.clearAllLayerAndEvent()
    ueManager.dasScene.stopSequence()
    // ueManager.resetCamera()
    router.back()
}
</script>
<style scoped lang="scss">
.building-scene-container {
    .scene-back-btn {
        z-index: 1000;
        position: absolute;
        width: 374px;
        height: 99px;
        font-size: 50px;
        color: #f5e9d2;
        cursor: pointer;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(33, 32, 29, 0.8);
        backdrop-filter: blur(4px);
        top: 52px;
        left: 108px;
        img {
            width: 74px;
            height: 39px;
            margin-right: 16px;
        }
        &::before {
            border-radius: 50px;
        }
    }
    .scene-detail-entrance {
        z-index: 1000;
        position: absolute;
        right: 0;
        bottom: 0;
        width: 1476.758px;
        height: 418.664px;
        background: url('@/assets/images/resource/scene_deco.png') center center / 100% 100%
            no-repeat;
        color: #f5e9d2;
        font-size: 24px;
        text-align: center;

        .scene-detail-btn {
            cursor: pointer;
            display: inline-block;
            position: absolute;
            img {
                width: 224px;
                height: 224px;
            }
            &.building {
                left: 160px;
                top: -40px;
            }
            &.relic {
                left: 600px;
                top: -200px;
            }
            &.culture {
                left: 1060px;
                top: -260px;
            }
        }
    }
}
</style>
