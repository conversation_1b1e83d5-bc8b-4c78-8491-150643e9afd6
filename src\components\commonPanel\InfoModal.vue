<template>
    <div class="info-video-modal">
        <img class="modal-close" src="@/assets/images/common/icon_close.png" @click="closeModal" />
        <div class="modal-content">
            <div class="detail-left">
                <video
                    class="detail-video"
                    controls
                    autoplay
                    ref="videoRef"
                    :src="props.detailData?.video"
                />
            </div>
            <div class="detail-right">
                <div class="detail-title">{{ props.detailData?.title }}</div>
                <div class="detail-remark">{{ props.detailData?.remark }}</div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
const emit = defineEmits(['close'])
const props = defineProps({
    detailData: {
        type: Object,
        default: () => {}
    }
})
const closeModal = () => {
    emit('close')
}
</script>

<style scoped lang="scss">
.info-video-modal {
    position: absolute;
    z-index: 1002;
    width: 1800px;
    height: 1600px;
    padding: 98px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(
        155deg,
        rgba(175, 167, 142, 0.8) -61.8%,
        rgba(141, 130, 99, 0.8) -37.56%,
        rgba(74, 65, 39, 0.8) -3.93%,
        rgba(17, 15, 9, 0.8) 39.86%,
        rgba(33, 32, 29, 0.8) 100.85%
    );

    border-radius: 8px;
    backdrop-filter: blur(8px);
    .modal-close {
        position: absolute;
        right: -50px;
        top: -50px;
        width: 100px;
        height: 100px;
        cursor: pointer;
        &:hover {
            filter: brightness(1.2);
        }
    }
    .modal-content {
        width: 100%;
        height: 100%;
        .detail-left {
            text-align: center;
            .detail-video {
                width: auto;
                height: 850px;
                object-fit: contain;
            }
        }
        .detail-right {
            .detail-title {
                margin-top: 30px;
                font-size: 70px;
                color: #e1bb82;
            }
            .detail-remark {
                margin-top: 30px;
                color: #fff;
                font-size: 50px;
                text-align: justify;
                line-height: 1.6;
                height: 960px;
                overflow-y: auto;
            }
        }
    }
}
</style>
