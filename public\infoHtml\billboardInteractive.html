<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>景点标牌</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* .container {
            position: relative;
            background: rgba(0, 0, 0, 0.6);
            padding: 45px;
            width: 1100px;
            height: 550px;
            font-size: 40px;
            z-index: 9999;
            border: 1px solid #FFE7C4;
            border-radius: 5px;
        }

        .info-desc {
            color: #FFE7C4;
            text-align: justify;
            line-height: 1.5;
        }

        .info-btn {
            position: absolute;
            right: 45px;
            bottom: 45px;
            width: 207px;
            height: 73px;
            line-height: 73px;
            font-size: 30px;
            display: inline-block;
            margin-top: 20px;
            color: #FFE3A6;
            text-align: center;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            background: url('../images/btn_more.png') center center / 100% 100% no-repeat;
        } */

        .info-btn:hover {
            filter: brightness(1.1);
        }
        .container {
            position: relative;
            background: rgba(0, 0, 0, 0.6);
            padding: 18px;
            width: 440px;
            height: 220px;
            font-size: 16px;
            z-index: 9999;
            border: 1px solid #FFE7C4;
            border-radius: 5px;
        }

        .info-desc {
            color: #FFE7C4;
            text-align: justify;
            line-height: 1.5;
        }

        .info-btn {
            position: absolute;
            right: 18px;
            bottom: 18px;
            width: 80px;
            height: 30px;
            line-height: 31px;
            font-size: 12px;
            display: inline-block;
            margin-top: 20px;
            color: #FFE3A6;
            text-align: center;
            border-radius: 4px;
            padding: 0px 10px;
            cursor: pointer;
            background: url('../images/btn_more.png') center center / 100% 100% no-repeat;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="info-container">
            <div class="info-desc">
                武当山遇真宫的建筑布局严格遵循道教"天人合一"理念，呈坐北朝南的轴线对称格局。主体建筑沿中轴线依次布置山门、龙虎殿、真仙殿、配殿等，形成三进院落空间，暗合"三才"之道。其布局特点有三：一是前宫后寝的礼制序列；二是"负阴抱阳"的风水格局；三是官式建筑与山地营建的巧妙结合，既显皇家气派又顺应自然地形。
            </div>
        </div>
        <div class="info-btn" onclick="clickMore()">了解更多</div>
    </div>
    <script>
        // const screenWidth = 3840;
        // document.querySelector('.container').style.zoom = screenWidth / 9600
        // const clickMore = () => {
        //     console.log('checkMore')
        // }
    </script>
</body>

</html>