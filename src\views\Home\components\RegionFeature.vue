<template>
    <div class="region-feature-box right-panel-content">
        <div class="info-title common-gradient-text">武当山风景区 AAAAA</div>
        <div class="info-subtitle">——世界文化遗产、中华道教圣地与国家重点风景名胜区</div>
        <div class="info-introduce">
            武当山，位于湖北省十堰市丹江口市的旅游景区，景区总面积312平方千米。武当山又名太和山，武当山属自然景观和人文景观完美结合的山岳型风景名胜区。 武当山风景区有太极湖、玄岳门、太子坡、南岩宫、金顶、紫霄宫、五龙宫，琼台八大景区。
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">景区概况</span>
        </div>
        <div class="info-items">
            <div class="info-items-row">
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-unit">约</span>
                        <span class="value-number"> {{ baseInfo.area }}</span>
                        <span class="value-unit">平方公里</span>
                    </div>
                    <div class="info-item-label">景区面积</div>
                </div>
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-unit">约</span>
                        <span class="value-number">{{ baseInfo.perimeter }}</span>
                        <span class="value-unit">公里</span>
                    </div>
                    <div class="info-item-label">景区周长</div>
                </div>
            </div>
            <div class="info-items-row">
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-name">天柱峰/</span>
                        <span class="value-unit">海拔</span>
                        <span class="value-number">{{ baseInfo.altitude }}</span>
                        <span class="value-unit">米</span>
                    </div>
                    <div class="info-item-label">最高峰</div>
                </div>
                <div class="info-item">
                    <div class="info-item-value">
                        <span class="value-unit">约</span>
                        <span class="value-number">{{ baseInfo.landscape }}</span>
                        <span class="value-unit">个</span>
                    </div>
                    <div class="info-item-label">自然/人文景观</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useDasUE } from '@/hooks/useDasUEHook'

const markerBasePath = window.webConfig.mapMarkerUrl
let ueManager = null
let pointsLayer = ref([]) // 点位图层 pointsLayer

const baseInfo = ref({
    area: 312,
    perimeter: 400,
    altitude: 1612,
    landscape: 400
})

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.resetCamera()
        addWdsPoint()
    })
})

onBeforeUnmount(() => {
    removeLayers()
})

const addWdsPoint = async () => {
    if (!ueManager) return
    const wdsData = {
        name: '武当山景区',
        texturePath: `${markerBasePath}/marker_wds.png`,
        points: [[111.0584609986337, 32.451462132620414, 302.34889543180464]],
        pointSize: [50, 170],
        enableDepthTest: false
    }
    const layer = await ueManager.dasPointsLayer.createInstance(wdsData)
    pointsLayer.value.push(layer)
}

// 移除图层（移除当前使用的或全部移除）
const removeLayers = async () => {
    // let pRoot = await ueManager.dasGroupLayer.getRoot()
    // let allLayers = await pRoot.getAllLayer()
    // allLayers.forEach(layer => {
    //     if (layer.id === pointsLayer.value[0].id) {
    //         pRoot.removeLayer(layer)
    //     }
    // })
    ueManager?.clearAllLayerAndEvent()
}
</script>

<style lang="scss" scoped>
.region-feature-box {
    height: 100%;
    text-align: right;
    .info-item {
        width: 30%;
    }
}
</style>
