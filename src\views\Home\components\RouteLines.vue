<template>
    <div class="route-lines-box right-panel-content">
        <div class="info-title common-gradient-text">古神道</div>
        <div class="info-subtitle">——万象朝圣·武当天阶神道</div>
        <div class="info-introduce">
            武当山上从各个方向登山的道路称为神道，连接着山上数百座庙宇。神道路面均由方整青石板满铺，上下坡处设石栏蹬道，蹬道条石上用方形铁桩固定，另于陡坡处石栏上还加设铁链，武当山直达金顶的神道共用九条。
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text" style="letter-spacing: 4px">主要朝圣路线</span>
        </div>
        <div class="info-content-list">
            <div
                class="route-item"
                v-for="(item, index) in routeList"
                :key="index"
                @click="onRouteItemClick(item)"
            >
                <div class="route-item-cover"></div>
                <div class="route-item-content">
                    <div class="route-item-title">{{ item.name }}</div>
                    <div class="route-item-info">
                        <div class="route-item-start">起始位置：遇真宫——太和宫</div>
                        <div class="route-item-distance">里程：13.3km</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDasUE } from '@/hooks/useDasUEHook'
let ueManager = null
const emit = defineEmits(['changeModuleTitle'])
const layerRoad = ref(null)
const currentRouteData = ref(null)
const routeList = ref([
    { name: '古盐道（东神道）', id: 1 },
    { name: '古盐道（东神道）', id: 2 },
    { name: '古盐道（东神道）', id: 3 },
    { name: '古盐道（东神道）', id: 4 },
    { name: '古盐道（东神道）', id: 5 },
    { name: '古盐道（东神道）', id: 6 }
])
onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(async () => {
        ueManager = dasUE
        await ueManager.resetCamera()
    })
})

onBeforeUnmount(() => {
    // ueManager.dasScene.stopSequence()
    if (layerRoad.value) {
        layerRoad.value.setVisible(false)
    }
})
// 点击绘制路线
const onRouteItemClick = async (item: { name: string; id: number }) => {
    if (!ueManager) return
    if (currentRouteData.value?.id === item.id) {
        currentRouteData.value = null
        emit('changeModuleTitle', '')
        // ueManager.dasScene.stopSequence()
        if (layerRoad.value) {
            layerRoad.value.setVisible(false)
        }
        ueManager.resetCamera()
    } else {
        currentRouteData.value = item
        emit('changeModuleTitle', item.name)
        // 关卡序列方式
        // ueManager.dasScene.flyToLocationLLH(
        //     [111.123477473847, 32.38733394720414, 6647.103878518449],
        //     {
        //         pitch: -32.669647951742625,
        //         yaw: -132.2415039447616,
        //         roll: 0
        //     },
        //     1
        // )
        // await ueManager.dasScene.setSequence(
        //     '/Game/C_WDS/WDS_Video/SD_Video/North_ShenDao_Sequence.North_ShenDao_Sequence'
        // )
        // ueManager.dasScene.playSequence()

        // 内部图层
        if (!layerRoad.value) {
            const layer = await ueManager.dasInnerLayer.createInstance({ TagName: 'JinDinRoad1' })
            if (layer) {
                layerRoad.value = layer
                layerRoad.value.setVisible(true)
                layerRoad.value.flyToThis()
            }
        } else {
            layerRoad.value.setVisible(true)
            layerRoad.value.flyToThis()
        }
    }
}
</script>

<style lang="scss" scoped>
.route-lines-box {
    height: 100%;
    text-align: right;
}

.info-content-list {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 20px;

    .route-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        cursor: pointer;
        margin-bottom: 60px;

        .route-item-cover {
            width: 200px;
            height: 200px;
            background: url('@/assets/images/demo/cover_2.png') center center / 100% auto no-repeat;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .route-item-content {
            text-align: left;
            flex: 1;

            .route-item-title {
                position: relative;
                font-size: 50px;
                color: #fff;
                font-weight: bold;
                margin-bottom: 12px;
                margin-top: -12px;
                display: inline-block;
                padding-bottom: 8px;
                &::before {
                    z-index: -1;
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 46px;
                    opacity: 0.5;
                    background: #b48841;
                    left: 0;
                    bottom: 5px;
                }
                &::after {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 4px;
                    opacity: 0.8;
                    background: linear-gradient(90deg, #ffe2b4 0%, rgba(153, 136, 108, 0) 100%);
                    left: 0;
                    bottom: 5px;
                }
            }

            .route-item-info {
                font-size: 37px;
                color: #ffe2b4;

                .route-item-start,
                .route-item-distance {
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}
</style>
