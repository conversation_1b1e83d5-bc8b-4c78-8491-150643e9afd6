<template>
    <div class="bottom-menu-bar">
        <div class="menu-items-container">
            <div
                v-for="(item, index) in currentMenuData"
                :key="index"
                class="menu-item"
                :class="{ active: navStore.currentNav === item.type }"
                @click="onChangeNav(item)"
            >
                <div class="menu-item-bg" :style="getMenuItemStyle(item)"></div>
                <span class="menu-item-text">{{ item.name }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useNavStore } from '@/stores/nav'
import { onMounted, computed } from 'vue'

// 导入所有菜单图片
import secondMenu1 from '@/assets/images/tool/second_menu_1.png'
import secondMenu1Active from '@/assets/images/tool/second_menu_1_active.png'
import secondMenu2 from '@/assets/images/tool/second_menu_2.png'
import secondMenu2Active from '@/assets/images/tool/second_menu_2_active.png'
import secondMenu3 from '@/assets/images/tool/second_menu_3.png'
import secondMenu3Active from '@/assets/images/tool/second_menu_3_active.png'
import secondMenu4 from '@/assets/images/tool/second_menu_4.png'
import secondMenu4Active from '@/assets/images/tool/second_menu_4_active.png'
import secondMenu5 from '@/assets/images/tool/second_menu_5.png'
import secondMenu5Active from '@/assets/images/tool/second_menu_5_active.png'
import secondMenu6 from '@/assets/images/tool/second_menu_6.png'
import secondMenu6Active from '@/assets/images/tool/second_menu_6_active.png'
import secondMenu7 from '@/assets/images/tool/second_menu_7.png'
import secondMenu7Active from '@/assets/images/tool/second_menu_7_active.png'
import secondMenu8 from '@/assets/images/tool/second_menu_8.png'
import secondMenu8Active from '@/assets/images/tool/second_menu_8_active.png'
import secondMenu9 from '@/assets/images/tool/second_menu_9.png'
import secondMenu9Active from '@/assets/images/tool/second_menu_9_active.png'
import secondMenu10 from '@/assets/images/tool/second_menu_10.png'
import secondMenu10Active from '@/assets/images/tool/second_menu_10_active.png'
import secondMenu11 from '@/assets/images/tool/second_menu_11.png'
import secondMenu11Active from '@/assets/images/tool/second_menu_11_active.png'

const emit = defineEmits(['changeNav'])
const props = defineProps({
    type: {
        type: String,
        default: 'home'
    }
})

const navStore = useNavStore()

// 创建图片映射对象
const menuImageMap = {
    '1': { normal: secondMenu1, active: secondMenu1Active },
    '2': { normal: secondMenu2, active: secondMenu2Active },
    '3': { normal: secondMenu3, active: secondMenu3Active },
    '4': { normal: secondMenu4, active: secondMenu4Active },
    '5': { normal: secondMenu5, active: secondMenu5Active },
    '6': { normal: secondMenu6, active: secondMenu6Active },
    '7': { normal: secondMenu7, active: secondMenu7Active },
    '8': { normal: secondMenu8, active: secondMenu8Active },
    '9': { normal: secondMenu9, active: secondMenu9Active },
    '10': { normal: secondMenu10, active: secondMenu10Active },
    '11': { normal: secondMenu11, active: secondMenu11Active }
}

const menuList = [
    {
        type: 'home',
        data: [
            {
                name: '区位特征',
                iconIndex: '1',
                type: 'regionFeature'
            },
            {
                name: '七十二峰朝大顶',
                iconIndex: '2',
                type: 'mountainLayout'
            },
            {
                name: '古神道',
                iconIndex: '3',
                type: 'routeLines'
            },
            {
                name: '遗产风貌',
                iconIndex: '4',
                type: 'heritageStyle'
            },
            {
                name: '建筑格局',
                iconIndex: '5',
                type: 'buildingLayout'
            }
        ]
    },
    {
        type: 'resource',
        data: [
            {
                name: '资源底座',
                iconIndex: '6',
                type: 'resource'
            },
            {
                name: '专题一张图',
                iconIndex: '7',
                type: 'specialSubject'
            },
            {
                name: '文物信息库',
                iconIndex: '8',
                type: 'relicInfos'
            }
        ]
    },
    {
        type: 'application',
        data: [
            {
                name: '文物保护',
                iconIndex: '9',
                type: 'relicProtect'
            },
            {
                name: '文旅运营',
                iconIndex: '10',
                type: 'tourismOperate'
            },
            {
                name: '景区安全',
                iconIndex: '11',
                type: 'spotSafety'
            }
        ]
    }
]

// 根据当前类型获取对应的菜单数据
const currentMenuData = computed(() => {
    const currentMenu = menuList.find(menu => menu.type === props.type)
    return currentMenu ? currentMenu.data : []
})

// 获取菜单项的背景样式
const getMenuItemStyle = (item: { iconIndex: string; type: string }) => {
    const isActive = navStore.currentNav === item.type
    const imageData = menuImageMap[item.iconIndex]

    if (!imageData) {
        return {
            backgroundImage: 'none',
            backgroundSize: '100% auto',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center'
        }
    }

    const imageUrl = isActive ? imageData.active : imageData.normal

    return {
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: '100% auto',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center'
    }
}

onMounted(() => {
    // 初始化时设置第一个菜单项为选中状态
    if (currentMenuData.value.length > 0 && !navStore.currentNav) {
        navStore.setCurrentNav(currentMenuData.value[0].type)
    }
})

const onChangeNav = (item: { iconIndex: string; type: string }) => {
    if (!['relicInfos'].includes(item.type)) {
        navStore.setCurrentNav(item.type)
    }
    emit('changeNav', item.type)
}
</script>

<style lang="scss" scoped>
.bottom-menu-bar {
    width: 3784px;
    height: 383.5px;
    position: absolute;
    z-index: 1000;
    left: 0;
    bottom: 0;
    background: url('@/assets/images/tool/bottom_menu.png') center center / 100% no-repeat;

    .menu-items-container {
        position: absolute;
        left: 929px;
        top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        .menu-item {
            width: 238px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 125px;

            .menu-item-bg {
                width: 238px;
                height: 222px;
                transition: all 0.3s ease;
            }

            .menu-item-text {
                color: #ffdfa7;
                font-size: 40px;
                margin-top: 20px;
                letter-spacing: 8px;
                white-space: nowrap;
            }
        }
    }
}
</style>
