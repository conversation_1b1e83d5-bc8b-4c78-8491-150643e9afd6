const markerBasePath = window.webConfig.mapMarkerUrl
export const buildingData = [
    {
        name: '遇真宫',
        texturePath: `${markerBasePath}/building_yuzhengong.png`, // 矩形小标注
        markerPath: `${markerBasePath}/marker_yuzhengong.png`, // 大标注
        sceneName: '<PERSON><PERSON><PERSON><PERSON><PERSON>', // 关卡名称
        pointName: '遇真宫', // 点位名称（场景内置点位，飞行用）
        value: 1,
        points: [[111.11942975937782, 32.50562688088714, 150.13863950448774]], // 标注点位
        cameraNear: {
            locationLLH: [111.11564730000002, 32.498400026320546, 858.696278097328],
            rotationLLH: {
                pitch: -39.47847378473089,
                yaw: -73.48188536889477,
                roll: 0
            }
        }, // 中景相机位置
        cameraPalace: {
            locationLLH: [111.11846120341686, 32.5037314347575, 239.13280159690527],
            rotationLLH: {
                pitch: -18.92582789510582,
                yaw: -87.80815626499928,
                roll: 0
            }
        } // 近景相机位置（子场景内）
    },
    {
        name: '紫霄宫',
        value: 2,
        texturePath: `${markerBasePath}/building_zixiaogong.png`,
        markerPath: `${markerBasePath}/marker_zixiaogong.png`,
        points: [[111.02222289106207, 32.42688727729715, 792.2787811654281]]
    },
    {
        name: '太和宫',
        value: 3,
        texturePath: `${markerBasePath}/building_taihegong.png`,
        markerPath: `${markerBasePath}/marker_taihegong.png`,
        pointName: '相机点位1',
        points: [[111.0035735671499, 32.40221921600622, 1462.6277383377078]],
        cameraNear: {
            locationLLH: [111.00623888161846, 32.40042032110401, 1625.8305615114746],
            rotationLLH: {
                pitch: -21.043920747402943,
                yaw: -161.0102150426106,
                roll: 0
            }
        },
        cameraPalace: {
            locationLLH: [111.00485636439839, 32.40081207738241, 1589.184165033297],
            rotationLLH: {
                pitch: -14.98957077731448,
                yaw: -176.5265412077137,
                roll: -0
            }
        }
    },
    {
        name: '玉虚宫',
        value: 4,
        texturePath: `${markerBasePath}/building_yuxugong.png`,
        markerPath: `${markerBasePath}/marker_yuxugong.png`,
        pointName: '相机点位2',
        points: [[111.07800350783897, 32.50662557859991, 170.95232311956505]]
    },
    {
        name: '南岩宫',
        value: 5
        // texturePath: `${markerBasePath}/building_xuanyuemen.png`,
        // markerPath: `${markerBasePath}/marker_nanyangong.png`,
        // points: [[110.87398559777553, 32.552662125968354, 375.70797279891065]]
    },
    { name: '五龙宫', value: 6 },
    { name: '净乐宫', value: 7 },
    { name: '朝天宫', value: 8 },
    { name: '清微宫', value: 9 },
    { name: '琼台观', value: 10 },
    { name: '回龙观', value: 11 },
    { name: '复真观', value: 12 },
    { name: '元和观', value: 13 },
    { name: '龙泉观', value: 14 }
]
