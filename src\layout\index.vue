<template>
    <div class="app-wrapper">
        <template v-if="isShowNavbar">
            <div class="display-logo">
                <img class="img-logo" src="@/assets/images/tool/display_logo.png" />
                <div class="web-title common-gradient-text">数字武当展示展览平台</div>
            </div>
            <navbar />
        </template>
        <app-main />
        <scene-viewer />
        <div class="right-top-tools">
            <control-tool />
            <time-weather-tool />
        </div>
        <ai-assistant />
    </div>
</template>

<script setup lang="ts">
import AppMain from './AppMain.vue'
import Navbar from './Navbar.vue'
import SceneViewer from '@/components/scene/SceneViewer.vue'
import TimeWeatherTool from '@/components/tools/TimeWeatherTool.vue'
import ControlTool from '@/components/tools/ControlTool.vue'
import AiAssistant from '@/components/tools/AiAssistant.vue'
import { useRouter } from 'vue-router'
import { useDasUE } from '@/hooks/useDasUEHook'
let ueManager = null
const router = useRouter()
const isShowNavbar = computed(() => {
    return ['/home', '/application', '/resource'].includes(router.currentRoute.value.fullPath)
})
onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.dasScene.setWeather('SetControlCloudIntensity', 0)
        ueManager.dasScene.setWeather('SetTimeOfDay', '11:00')
    })
})

onBeforeUnmount(() => {
    ueManager?.clearAllLayerAndEvent()
})
</script>

<style scoped>
.app-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-size: 100%;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            ellipse at center,
            transparent 0%,
            transparent 30%,
            rgba(0, 0, 0, 0.2) 50%,
            rgba(0, 0, 0, 0.3) 70%,
            rgba(0, 0, 0, 0.5) 85%,
            rgba(0, 0, 0, 0.8) 100%
        );
        pointer-events: none;
        z-index: 1;
    }
    .display-logo {
        position: absolute;
        z-index: 1001;
        top: 30px;
        left: 74px;
        display: flex;
        align-items: center;
        .img-logo {
            width: 650px;
            height: 162px;
        }
        .img-title {
            width: 500px;
            height: 162px;
            margin-left: 10px;
        }
        .web-title {
            font-size: 100px;
            margin-left: 43px;
            font-weight: 600;
            letter-spacing: 10px;
        }
    }
    .right-top-tools {
        position: absolute;
        z-index: 1001;
        top: 30px;
        right: 77px;
        display: flex;
        align-items: center;
    }
}
</style>
