import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import VueDevTools from 'vite-plugin-vue-devtools'
import { resolve } from 'path'

//大屏适配 引入postcss-pxtorem
import postCssPxToRem from 'postcss-pxtorem'

//自动引入
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        vue(),
        VueDevTools(),
        AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/ // .md
            ],
            imports: ['vue', 'vue-router', 'pinia'],
            eslintrc: {
                enabled: true, // 默认false, true启用。生成一次就可以，避免每次工程启动都生成
                filepath: './eslintrc-auto-import.json', // 生成json文件
                globalsPropValue: true
            },
            // 声明文件生成位置和文件名称
            dts: './src/auto-import.d.ts',
            resolvers: [
                ElementPlusResolver()
                // // 自动导入图标组件
                // IconsResolver({
                //   prefix: 'Icon'
                // })
            ]
        }),
        Components({
            resolvers: [
                // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
                ElementPlusResolver()
                // // 自动注册图标组件
                // IconsResolver({
                //   enabledCollections: ['ep']
                // })
            ]
        })
    ],
    resolve: {
        alias: {
            '@': resolve('./src')
        }
    },
    server: {
        host: '0.0.0.0'
    },
    css: {
        postcss: {
            plugins: [
                postCssPxToRem({
                    rootValue: 960, // 设计稿宽度的1/10
                    propList: ['*'], // 所有属性转换
                    selectorBlackList: [
                        'norem',
                        /^vue$/,
                        'vue-devtools__anchor--glowing',
                        'vue-devtools__panel',
                        'vue-devtools__anchor-btn',
                        'vue-inspector-floats',
                        'vue-inspector-card'
                    ], // 不进行rem转换
                    exclude: '/src/views/demo|node_modules'
                })
            ]
        }
    },
    build: {
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true
            }
        }
    }
})
