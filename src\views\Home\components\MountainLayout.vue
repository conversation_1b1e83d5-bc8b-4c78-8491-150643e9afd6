<template>
    <div class="mountain-layout-box right-panel-content">
        <div class="info-title common-gradient-text">七十二峰朝大顶</div>
        <div class="info-subtitle">——武当特色自然景观布局</div>
        <div class="info-introduce">
            武当山古建筑群始建于唐贞观年间（627—649年），宋、元时期均有建设。明永乐十年（1412年），明成祖朱棣在武当山敕建宫观殿堂，兴建了以金顶为核心的大批道教宫、观、祠、庙，形成了九宫、八观、三十六庵堂、七十二岩庙的古建筑群体系。现存古建筑49处，这些建筑集中体现了中国元、明、清三代世俗和宗教建筑的建筑学和艺术成就。
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text" style="letter-spacing: 4px">主要自然景观</span>
        </div>
        <div class="info-content-list">
            <div class="card-list-container">
                <div class="card-list">
                    <div
                        class="card-row"
                        v-for="(row, rowIndex) in currentPageData"
                        :key="rowIndex"
                    >
                        <div class="card-item" v-for="item in row" :key="item.value">
                            <div class="card-item-cover"></div>
                            <div class="card-item-name">{{ item.name }}</div>
                        </div>
                    </div>
                </div>
                <img
                    class="prev-icon"
                    src="@/assets/images/common/arrow_left.png"
                    @click="prevPage"
                    :class="{ disabled: currentPage === 0 }"
                />
                <img
                    class="next-icon"
                    src="@/assets/images/common/arrow_right.png"
                    @click="nextPage"
                    :class="{ disabled: currentPage === totalPages - 1 }"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useDasUE } from '@/hooks/useDasUEHook'
let ueManager = null
const mountainList = ref([
    { name: '天柱峰', value: '1' },
    { name: '万丈峰', value: '2' },
    { name: '狮子峰', value: '3' },
    { name: '玄天峰', value: '4' },
    { name: '天柱峰', value: '5' },
    { name: '万丈峰', value: '6' },
    { name: '狮子峰', value: '7' },
    { name: '玄天峰', value: '8' },
    { name: '万丈峰', value: '9' },
    { name: '狮子峰', value: '10' },
    { name: '玄天峰', value: '11' },
    { name: '天柱峰', value: '12' },
    { name: '万丈峰', value: '13' },
    { name: '狮子峰', value: '14' }
])

// 分页相关
const currentPage = ref(0)
const itemsPerPage = 10 // 每页10个项目
const itemsPerRow = 5 // 每行5个项目
const layerMountain = ref(null) // 72峰内部图层

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(async () => {
        ueManager = dasUE
        ueManager.dasScene.flyToLocationLLH(
            [111.01400285268252, 32.33936482473213, 4580.136728887209],
            { pitch: -30.602663707216166, yaw: -96.67823879279435, roll: -0 },
            1
        )
        const layer = await ueManager.dasInnerLayer.createInstance({ TagName: '72PeakRoot' })
        if (layer) {
            layerMountain.value = layer
            layerMountain.value.setVisible(true)
            // layerMountain.value.flyToThis()
        }
    })
})

onBeforeUnmount(() => {
    if (layerMountain.value) {
        layerMountain.value.setVisible(false)
    }
})

// 计算总页数
const totalPages = computed(() => {
    return Math.ceil(mountainList.value.length / itemsPerPage)
})

// 计算当前页的数据，分为两行
const currentPageData = computed(() => {
    const startIndex = currentPage.value * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const pageItems = mountainList.value.slice(startIndex, endIndex)

    // 将数据分为两行，每行5个
    const rows = []
    for (let i = 0; i < pageItems.length; i += itemsPerRow) {
        rows.push(pageItems.slice(i, i + itemsPerRow))
    }
    return rows
})

// 上一页
const prevPage = () => {
    if (currentPage.value > 0) {
        currentPage.value--
    }
}

// 下一页
const nextPage = () => {
    if (currentPage.value < totalPages.value - 1) {
        currentPage.value++
    }
}
</script>

<style lang="scss" scoped>
.mountain-layout-box {
    height: 100%;
    text-align: right;
}
</style>
