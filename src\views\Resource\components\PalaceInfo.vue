<template>
    <div class="palace-infos-box">
        <div class="detail-container">
            <img
                class="palace-close"
                src="@/assets/images/resource/icon_close.png"
                @click="emit('close')"
            />
            <div class="palace-title">{{ props.detailData?.name || '-' }}</div>
            <img class="image-content" fit="cover" src="@/assets/images/demo/cover_3.png" />
            <div class="audio-content">
                <div class="audio-title">解说音频</div>
                <AudioBar :audio-src="props.detailData?.audio" />
            </div>
            <div class="remark-content">
                遇真宫位于武当山镇东4公里处，属武当山九宫之一，
                位于山麓，海拔174.7米，背依凤凰山，面对九龙山，左为望仙台，右为黑虎洞，山水环绕如城，旧名黄土城。此宫周围高山环抱，溪流潺潺，大树参天。
                明代初期张三丰在此修炼，永乐年间皇帝命令在此地此宫周围高山环抱，溪流潺潺，大树参天。
            </div>
            <div class="years-list">
                <div v-for="(item, index) in yearsList" :key="index" class="years-item">
                    <div class="years-item-title">{{ item.title }}</div>
                    <div class="years-item-text">
                        <div class="years-intro">起源</div>
                        <div class="years-value">前210年</div>
                    </div>
                    <div class="years-item-box">
                        “武当”之名最早见于汉代。汉高祖五年置武当县，属汉中郡。《太平寰宇记》：“取武当山以为县名。”
                    </div>
                </div>
            </div>
        </div>
        <img
            class="enter-palace-btn"
            @click="emit('goPalace')"
            src="@/assets/images/resource/palace_enter.png"
        />
    </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close', 'goPalace'])
const props = defineProps({
    detailData: {
        type: Object,
        default: () => {}
    }
})
const yearsList = [{ title: '汉' }, { title: '唐' }, { title: '宋' }, { title: '明' }]
</script>

<style lang="scss" scoped>
.palace-infos-box {
    width: 1262px;
    height: 1776px;
    position: fixed;
    left: 62%;
    top: 50%;
    z-index: 9999;
    transform: translate(-50%, -50%);
    background: url('@/assets/images/resource/bg_palace.png') center center / 100% no-repeat;
    padding: 120px 90px 60px 90px;
    text-align: left;
    .detail-container {
        width: 100%;
        height: 1500px;
        .palace-title {
            color: #e1bb82;
            font-size: 55px;
            font-weight: 900;
            letter-spacing: 5px;
            padding: 0 250px 40px 250px;
            margin-bottom: 47px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            background: url('@/assets/images/common/underline_title.png') center bottom / 393px auto
                no-repeat;
        }
        .image-content {
            width: 100%;
            height: 393px;
            object-fit: cover;
        }
        .audio-content {
            margin: 20px 0;
            .audio-title {
                color: #e1bb82;
                font-size: 40px;
                margin-bottom: 23px;
            }
        }
        .remark-content {
            font-size: 30px;
            color: #fff;
            line-height: 1.6;
            text-align: justify;
            padding-right: 20px;
            max-height: 810px;
            overflow-y: auto;
        }
        .years-list {
            color: #fff;
            max-height: 450px;
            overflow-y: auto;
            margin-top: 30px;
            padding-right: 10px;
            .years-item {
                display: flex;
                align-items: center;
                margin-bottom: 30px;
            }
            .years-item-title {
                width: 90px;
                height: 90px;
                line-height: 90px;
                background: url('@/assets/images/common/icon_year.png') center center / 100%
                    no-repeat;
                color: #fff4e4;
                text-align: center;
                font-size: 34px;
                font-weight: 900;
            }
            .years-item-text {
                width: 120px;
                text-align: center;
                position: relative;
                padding-right: 10px;
                .years-intro {
                    font-size: 24px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #e1bb8280;
                }
                .years-value {
                    font-size: 17px;
                    color: #a3a3a3;
                    padding-top: 10px;
                }
                &::after {
                    content: '';
                    position: absolute;
                    width: 25px;
                    height: 25px;
                    right: -12px;
                    top: calc(50% - 12px);
                    background: url('@/assets/images/common/icon_taiji.png') center center / 100%
                        no-repeat;
                }
            }
            .years-item-box {
                font-size: 24px;
                flex: 1;
                width: 858px;
                height: 125px;
                border: 1px solid #e1bb821a;
                display: flex;
                align-items: center;
                padding: 0 20px;
                background: rgba(255, 255, 255, 0.1) url('@/assets/images/common/icon_card_1.png')
                    bottom right / 205px 102px no-repeat;
            }
        }
    }
    .palace-close {
        position: absolute;
        cursor: pointer;
        width: 50px;
        height: 50px;
        top: 50px;
        right: 0;
    }
    .enter-palace-btn {
        position: absolute;
        cursor: pointer;
        width: 445.747px;
        height: 65.167px;
        bottom: 65px;
        left: 50%;
        transform: translate(-50%, 0);
    }
}
</style>
