<template>
    <div class="resource-detail-modal common-modal-border">
        <img class="modal-close" src="@/assets/images/common/icon_close.png" @click="closeModal" />
        <div class="modal-content-left">
            <div class="res-title">玉虚古银杏树</div>
            <div class="res-infos">
                <div
                    class="res-info-item"
                    v-for="(item, index) in resourceDetail.info"
                    :key="index"
                >
                    <div class="item-label">{{ item.label }}</div>
                    <div class="item-value">{{ item.value }}</div>
                </div>
            </div>
        </div>
        <div class="modal-content-right">
            <div class="res-title">
                <div class="res-title-text">{{ resourceDetail.title }}</div>
                <div class="res-title-type">{{ resourceDetail.typeName }}</div>
            </div>
            <div class="res-content">
                <div class="res-preview">
                    <el-image
                        class="res-preview-image"
                        fit="contain"
                        v-if="previewItem.type === 2"
                        :src="previewItem.image"
                        :preview-src-list="[previewItem.image]"
                    ></el-image>
                    <video
                        class="res-preview-video"
                        controls
                        v-if="previewItem.type === 3"
                        :src="previewItem.video"
                    ></video>
                    <audio
                        class="res-preview-audio"
                        controls
                        v-if="previewItem.type === 4"
                        :src="previewItem.audio"
                    ></audio>
                </div>
                <div class="res-child-list">
                    <div class="res-types">
                        <div
                            class="res-type-item"
                            :class="{ active: activeType == item.value }"
                            v-for="item in resTypes"
                            :key="item.value"
                            @click="changeResType(item.value)"
                        >
                            <span class="res-type-name">{{ item.label }}</span>
                            <div class="res-type-circle"></div>
                        </div>
                    </div>
                    <Swiper class="res-list" :slides-per-view="6" :space-between="15">
                        <SwiperSlide
                            class="res-item"
                            :class="{ active: previewItem?.id == item.id }"
                            v-for="item in resList"
                            :key="item.id"
                            @click="changeRes(item)"
                        >
                            <div class="res-item-cover"></div>
                            <div class="res-item-info">
                                <div class="res-item-name">{{ item.name }}</div>
                                <img src="@/assets/images/resource/res_name_hr.png" />
                                <div>资源类型</div>
                            </div>
                        </SwiperSlide>
                    </Swiper>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
const staticBasePath = window.webConfig.staticResourceUrl
const emit = defineEmits(['close'])
const closeModal = () => {
    emit('close')
}
const activeType = ref(0)
const resourceDetail = ref({
    title: '玉虚古银杏树',
    typeName: '三维模型',
    info: [
        { label: '位置', value: '十堰市武当山玉虚宫' },
        { label: '形态', value: '植被' },
        { label: '编号', value: '201906809-2' },
        { label: '树种', value: '古银杏' },
        { label: '树龄（年）', value: '100' },
        { label: '树高（m）', value: '11' },
        { label: '管理单位', value: '林业局' },
        { label: '保护等级', value: 'xx道长种植' },
        { label: '来源', value: '植被' },
        { label: '生长环境', value: '深山' },
        { label: '收录时间', value: '2005年6月20日' },
        {
            label: '简介',
            value: '在十堰市武当山玉虚宫后山深处，一处悬崖峭壁之上，长着一棵古银杏，树龄有12000年以上，妥妥的植物“活化石”。这棵古银杏在悬崖上长了千万年，苍劲古朴，树冠横展。树木基部巨大，又生长出年龄、高矮不同的22枝复干，环围达到10米。'
        }
    ]
})
const previewItem = ref({})

const resTypes = ref([
    { label: '所有资源', value: 0 },
    { label: '三维模型', value: 1 },
    { label: '图片', value: 2 },
    { label: '视频', value: 3 },
    { label: '音频', value: 4 }
])

const resList = ref([
    { name: '明代铜铸真武坐像.png', id: 1, type: 2 },
    { name: '明代铜铸真武坐像明代铜铸真武坐像.png', id: 2, type: 2 },
    { name: '明代铜铸真武坐像.png', id: 3, type: 3 },
    { name: '明代铜铸真武坐像.png', id: 4, type: 4 },
    { name: '明代铜铸真武坐像.png', id: 5, type: 2 },
    { name: '明代铜铸真武坐像.png', id: 6, type: 3 },
    { name: '明代铜铸真武坐像.png', id: 6, type: 2 }
])

// 切换资源类型
const changeResType = type => {
    activeType.value = type
}
// 切换当前资源
const changeRes = item => {
    previewItem.value = {
        ...item,
        image: `${staticBasePath}/img1.jpeg`,
        audio: ``,
        video: `${staticBasePath}/lhd_part.mp4`
    }
}

onMounted(() => {
    changeRes({
        ...resList.value[0],
        image: `${staticBasePath}/img1.jpeg`,
        audio: ``,
        video: `${staticBasePath}/lhd_part.mp4`
    })
})
</script>
<style scoped lang="scss">
.resource-detail-modal {
    position: absolute;
    z-index: 1002;
    width: 2800px;
    height: 1600px;
    padding: 57px 85px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(33, 32, 29, 0.8);
    border-radius: 15px;
    backdrop-filter: blur(8px);
    &::before {
        border-radius: 15px;
    }
    .modal-close {
        position: absolute;
        right: -50px;
        top: -50px;
        width: 100px;
        height: 100px;
        cursor: pointer;
        &:hover {
            filter: brightness(1.2);
        }
    }
    display: flex;
    .modal-content-left {
        width: 864px;
        .res-title {
            color: #e1bb82;
            font-size: 70px;
            font-weight: 900;
            margin-bottom: 30px;
        }
        .res-infos {
            height: 1340px;
            border-radius: 9px;
            background: rgba(240, 220, 169, 0.05);
            color: #fff;
            font-size: 30px;
            padding: 40px 30px;
            overflow-y: auto;
            .res-info-item {
                display: flex;
                margin-bottom: 25px;
                .item-label {
                    width: 150px;
                    margin-right: 20px;
                }
                .item-value {
                    flex: 1;
                    text-align: justify;
                }
            }
        }
    }
    .modal-content-right {
        width: calc(100% - 864px);
        position: relative;
        padding-left: 50px;
        .res-title {
            position: absolute;
            display: flex;
            right: 0;
            top: 0;
            .res-title-text {
                width: 76px;
                margin-right: 50px;
                font-size: 70px;
                font-weight: 900;
                background: linear-gradient(
                    90deg,
                    #d8d4a3 8.48%,
                    #fbf7c3 25.45%,
                    #e7d9a2 46.06%,
                    #ccb38e 81.21%,
                    #af9a6a 100%
                );
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
                text-align: center;
            }
            .res-title-type {
                display: flex;
                align-items: center;
                margin-top: 25px;
                width: 84px;
                height: 365px;
                font-size: 50px;
                font-weight: 900;
                color: #7d1211;
                text-align: center;
                line-height: 62px;
                background: url('@/assets/images/resource/resource_type.png') center center / 100%
                    100% no-repeat;
            }
        }
        .res-content {
            .res-preview {
                height: 980px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 40px;
                .res-preview-image {
                    height: 100%;
                }
                audio::-webkit-media-controls-enclosure {
                    background-color: rgba(255, 255, 255, 0.8);
                    border-radius: 4px;
                }
                .res-preview-audio {
                    width: 55%;
                }
                .res-preview-video {
                    width: 90%;
                    height: auto;
                }
            }
            .res-child-list {
                height: 500px;
                .res-types {
                    display: flex;
                    font-size: 28px;
                    color: #a1a1a1;
                    .res-type-item {
                        position: relative;
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        &::after {
                            content: '';
                            position: absolute;
                            width: 100%;
                            height: 1px;
                            bottom: 18px;
                            background: #a1a1a1;
                            z-index: 1;
                            transition: all 0.3s ease;
                        }
                        .res-type-name {
                            width: 100%;
                            height: 66px;
                            text-align: center;
                            background: url('@/assets/images/resource/icon_resource_type.png')
                                center center / 81px 66px no-repeat;
                            display: flex;
                            align-items: flex-end;
                            justify-content: center;
                        }
                        .res-type-circle {
                            width: 18px;
                            height: 18px;
                            border-radius: 50%;
                            border: 2px solid #a1a1a1;
                            background: #221d19;
                            margin: 8px 0;
                            z-index: 2;
                        }
                        &.active {
                            color: #e1bb82;
                            .res-type-circle {
                                border: 2px solid #e1bb82;
                            }
                            &::after {
                                // background: linear-gradient(
                                //     to right,
                                //     rgba(225, 187, 130, 0.4) 0%,
                                //     rgba(225, 187, 130, 1) 50%,
                                //     rgba(225, 187, 130, 0.4) 100%
                                // );
                                background: #e1bb82;
                            }
                        }
                    }
                }
                .res-list {
                    width: 100%;
                    margin-top: 50px;
                    .res-item {
                        position: relative;
                        cursor: pointer;
                        height: 330px;
                        border: 1px solid #a1a1a1;
                        color: #a1a1a1;
                        &.active {
                            &::after {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: url('@/assets/images/resource/bg_resource.png') center
                                    center / 100% 100% no-repeat;
                            }
                        }
                        .res-item-cover {
                            height: 218px;
                            background: url('@/assets/images/demo/cover_3.png') center center /
                                cover no-repeat;
                            margin-bottom: 10px;
                        }
                        .res-item-info {
                            font-size: 20px;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            padding: 0 20px;
                            .res-item-name {
                                width: 100%;
                                font-size: 20px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                            img {
                                width: 200px;
                                margin: 10px 0;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
