<template>
    <div class="resource-container">
        <RightPanelBox>
            <template #content>
                <div class="demo-right-content">
                    <!-- <img
                        class="demo-right-img demo-right-img-1"
                        src="@/assets/images/demo/app_right_1.png"
                        v-if="currentNav === 'relicProtect'"
                    /> -->
                    <RelicProtect v-if="currentNav === 'relicProtect'" />
                    <img
                        class="demo-right-img"
                        src="@/assets/images/demo/app_right_2.png"
                        v-if="currentNav === 'tourismOperate'"
                    />
                    <img
                        class="demo-right-img"
                        src="@/assets/images/demo/app_right_3.png"
                        v-if="currentNav === 'spotSafety'"
                    />
                </div>
            </template>
        </RightPanelBox>
        <InfoModal v-if="isShowInfo" :detail-data="detailInfo" @close="isShowInfo = false" />
        <BottomMenu type="application" @change-nav="onChangeNav" />
    </div>
</template>
<script setup lang="ts">
import BottomMenu from '@/components/tools/BottomMenu.vue'
import RightPanelBox from '@/components/commonPanel/RightPanelBox.vue'
import InfoModal from '@/components/commonPanel/InfoModal.vue'
import RelicProtect from './components/RelicProtect.vue'
import { useNavStore } from '@/stores/nav'
import { useDasUE } from '@/hooks/useDasUEHook'
import { buildingData } from '@/config/buildingData'

const markerBasePath = import.meta.env.VITE_WDS_MARKER_PATH
const staticBasePath = import.meta.env.VITE_WDS_STATIC_PATH
let ueManager = null
let pointsLayer = ref([]) // 点位图层 pointsLayer
let isShowInfo = ref(false)
let detailInfo = ref({
    video: `${staticBasePath}/yzg_protect.m4v`,
    title: '遇真宫顶升工程',
    remark: '因南水北调工程，遇真宫面临被淹没的危险。2012年，在原址实施垫高保护工程，将遇真宫的山门、东、西宫门原地顶升15米，创造了世界古建筑整体顶升的纪录。'
})
const navStore = useNavStore()
const currentNav = computed(() => navStore.currentNav) // 当前选中的二级菜单

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.dasSelectTool.initSelectTool({
            onClick: obj => onMarkerClick(obj)
        })
        addVideoPoint()
    })
})
onBeforeUnmount(() => {
    ueManager?.clearAllLayerAndEvent()
})
// 切换子菜单
const onChangeNav = async (type: string) => {
    await ueManager.clearAllLayers()
    switch (type) {
        case 'relicProtect':
            addVideoPoint()
            break
        case 'tourismOperate':
            addTourismPoint()
            break
        case 'spotSafety':
            addSafePoint()
            break
    }
}

// 添加一个顶升动画点位
const addVideoPoint = async () => {
    if (!ueManager) return
    let cameraNear: unknown = buildingData.find(item => item.name === '遇真宫').cameraNear
    ueManager.dasScene.flyToLocationLLH(cameraNear.locationLLH, cameraNear.rotationLLH)
    pointsLayer.value = []
    const wdsData = {
        name: '顶升动画点位置',
        texturePath: `${markerBasePath}/marker_yuzhendingsheng.png`,
        points: [[111.11722680503301, 32.505788056227104, 150.09810659316278]],
        pointSize: [200, 35],
        enableDepthTest: false
    }
    const layer = await ueManager.dasPointsLayer.createInstance(wdsData)
    pointsLayer.value.push(layer)
}

// 文旅点位
const addTourismPoint = async () => {
    if (!ueManager) return
    let cameraNear: unknown = buildingData.find(item => item.name === '太和宫').cameraNear
    ueManager.dasScene.flyToLocationLLH(cameraNear.locationLLH, cameraNear.rotationLLH)
    const data = [
        {
            name: '1',
            value: 1,
            texturePath: `${markerBasePath}/wenlv_1.png`,
            points: [[111.00347247552872, 32.40021723735486, 1510.9789360756206]],
            pointSize: [320, 175],
            enableDepthTest: false
        },
        {
            name: '12',
            value: 2,
            texturePath: `${markerBasePath}/wenlv_2.png`,
            points: [[111.00439560564843, 32.40076502471974, 1570.962986819544]],
            pointSize: [320, 175],
            enableDepthTest: false
        },
        {
            name: '3',
            value: 3,
            texturePath: `${markerBasePath}/point_1.png`,
            points: [[111.00467938756037, 32.40010427497221, 1519.6388851734157]],
            pointSize: [200, 50],
            enableDepthTest: false
        },
        {
            name: '4',
            value: 4,
            texturePath: `${markerBasePath}/point_2.png`,
            points: [[111.00488487908375, 32.40100058559706, 1548.8322317740171]],
            pointSize: [200, 50],
            enableDepthTest: false
        }
    ]
    await ueManager.dasPointsLayer.batchCreateInstance(data)
}

// 安全点位
const addSafePoint = async () => {
    if (!ueManager) return
    let cameraNear: unknown = buildingData.find(item => item.name === '太和宫').cameraNear
    ueManager.dasScene.flyToLocationLLH(cameraNear.locationLLH, cameraNear.rotationLLH)
    const data = [
        {
            name: '1',
            value: 1,
            texturePath: `${markerBasePath}/anquan_1.png`,
            points: [[111.00347247552872, 32.40021723735486, 1510.9789360756206]],
            pointSize: [240, 200],
            enableDepthTest: false
        },
        {
            name: '12',
            value: 2,
            texturePath: `${markerBasePath}/anquan_2.png`,
            points: [[111.00439560564843, 32.40076502471974, 1570.962986819544]],
            pointSize: [46, 50],
            enableDepthTest: false
        },
        {
            name: '3',
            value: 3,
            texturePath: `${markerBasePath}/anquan_2.png`,
            points: [[111.00467938756037, 32.40010427497221, 1519.6388851734157]],
            pointSize: [46, 50],
            enableDepthTest: false
        },
        {
            name: '4',
            value: 4,
            texturePath: `${markerBasePath}/anquan_2.png`,
            points: [[111.00488487908375, 32.40100058559706, 1548.8322317740171]],
            pointSize: [46, 50],
            enableDepthTest: false
        }
    ]
    await ueManager.dasPointsLayer.batchCreateInstance(data)
}

const onMarkerClick = (obj: unknown) => {
    const item = obj.message ? JSON.parse(obj.message) : {}
    if (item.selectLayer?.class == 'DasPointsLayer') {
        console.log('点击了点位', item)
        if (item.selectLayer.id === pointsLayer.value[0]?.id) {
            isShowInfo.value = true
        }
    }
}
</script>
<style scoped lang="scss">
.resource-container {
    .test-btns {
        position: absolute;
        z-index: 1000;
        left: 2200px;
        top: 500px;
    }
    .demo-right-content {
        width: 100%;
        text-align: right;
        .demo-right-img {
            width: 80%;
            height: auto;
            object-fit: contain;
        }
        .demo-right-img-1 {
            width: 90%;
        }
    }
}
</style>
