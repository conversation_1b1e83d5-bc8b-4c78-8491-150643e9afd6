@mixin clearfix {
    &:after {
        content: '';
        display: table;
        clear: both;
    }
}

@mixin scrollBar {
    /*定义整个滚动条高宽及背景：高宽分别对应横竖滚动条的尺寸*/
    &::-webkit-scrollbar {
        width: 6px;
        background-color: rgba(4, 37, 61, 0.6);
    }

    /*定义滚动条轨道：内阴影+圆角*/
    &::-webkit-scrollbar-track {
        background-color: rgba(4, 37, 61, 0.6);
    }

    /*定义滑块：内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background-color: #04a7ff79;
    }
}

@mixin relative {
    position: relative;
    width: 100%;
    height: 100%;
}
