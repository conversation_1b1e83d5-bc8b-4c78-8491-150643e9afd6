import Viewer from '@/components/dasUE/Viewer'
import { ref, onBeforeUnmount, onMounted, onBeforeMount } from 'vue'
import { LoadingStatusEnum } from './type'

const { promise: readyPromise, resolve } = Promise.withResolvers()
globalThis.readyPromise = readyPromise

const loadingStatus = ref<LoadingStatusEnum>(LoadingStatusEnum.BeforeLoad)
const cacheObject = {}

export function useRTCHook(domId: string) {
    const initViewer = () => {
        cacheObject.viewer = new Viewer({
            InputControllers: [],
            onInitialize: () => {
                console.log('Viewer initialized')
                loadingStatus.value = LoadingStatusEnum.Loaded
                resolve(cacheObject)
            },
            useUrlParams: false,
            hideDefaultUI: true,
            signalServer: 'ws://localhost:8876'
        })
        loadingStatus.value = LoadingStatusEnum.Loading
    }

    const resetLoadingStatus = () => {
        loadingStatus.value = LoadingStatusEnum.BeforeLoad
    }

    onBeforeMount(() => {
        initViewer()
    })

    onMounted(async () => {
        const { viewer } = cacheObject
        const targetDom = document.getElementById(domId)
        targetDom.appendChild(viewer.rootElement)
    })

    onBeforeUnmount(() => {
        resetLoadingStatus()
    })
}
