import { Configuration, IConfiguration } from './Confifuration'
import {
    Scene,
    Object3D,
    WebGLRenderer,
    PerspectiveCamera,
    AmbientLight,
    DirectionalLight,
    Box3,
    Vector3,
    Mesh,
    HemisphereLight,
    Group,
    AnimationMixer,
    Clock,
    MeshStandardMaterial
} from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js'
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader.js'
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js'

export default class ModelViewer {
    config: Configuration
    private scene: Scene
    private model?: Object3D
    private renderer: WebGLRenderer
    private camera: PerspectiveCamera
    private control: OrbitControls
    private mixer?: AnimationMixer
    private clock: Clock
    constructor(options: IConfiguration) {
        this.config = new Configuration(options)
        //! 创建场景
        this.scene = new Scene()
        //! 创建相机
        this.camera = new PerspectiveCamera(
            75,
            this.config.dom.clientWidth / this.config.dom.clientHeight,
            0.1,
            999999
        )
        //! 创建渲染器
        this.renderer = new WebGLRenderer({ antialias: true, alpha: true })
        this.renderer.setPixelRatio(window.devicePixelRatio)
        this.renderer.setSize(this.config.dom.clientWidth, this.config.dom.clientHeight)
        this.config.dom.appendChild(this.renderer.domElement)
        //! 设置循环
        this.renderer.setAnimationLoop(this.render.bind(this))

        //! 初始化控制器
        this.control = new OrbitControls(this.camera, this.renderer.domElement)
        this.control.enableDamping = true
        this.control.minDistance = 0.1
        this.control.maxDistance = 999999
        this.control.target.set(0, 0, 1)
        this.control.update()
        this.control.enablePan = false

        // //! 光照/背景
        const lightGroup = new Group()
        this.scene.add(lightGroup)
        lightGroup.add(new AmbientLight(0xffffff, 0.5))
        // lightGroup.add(new DirectionalLight(0xffffff, 2));
        lightGroup.add(new HemisphereLight(0xffffff, 0xffffff, this.config.lightIntensity))

        //!clock
        this.clock = new Clock()

        //! 缩放
        window.addEventListener('resize', this.onWindowResize.bind(this), false)
    }

    private render(): void {
        this.control.update()
        //! 自动旋转
        if (this.model && this.config.autoRotate) {
            this.model.rotation.y -= 0.005
            if (this.mixer) {
                this.mixer.update(this.clock.getDelta())
            }
        }
        this.renderer.render(this.scene, this.camera)
    }

    public placeCameraAndLight(): void {
        if (!this.model) return
        //! 计算模型的包围盒
        const box = new Box3().setFromObject(this.model)
        // 获取模型的中心点和大小
        const center = box.getCenter(new Vector3())
        const size = box.getSize(new Vector3())

        // 找出模型最大的尺寸
        const maxDim = Math.max(size.x, size.y, size.z)

        //! 设置相机位置

        // 根据模型大小计算合适的相机位置
        const fov = this.camera.fov * (Math.PI / 180) // 将角度转换为弧度
        const cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2))

        // 确保相机的 near 和 far 面包住模型
        const minZ = box.min.z
        const cameraToFarEdge = minZ < 0 ? -minZ + cameraZ : cameraZ - minZ
        const cameraHeight = maxDim * 0.5
        this.camera.position.set(center.x, center.y + cameraHeight, cameraZ)
        this.camera.updateProjectionMatrix()
        this.control.minDistance = cameraToFarEdge / 3
        this.control.maxDistance = (cameraToFarEdge * 4) / 3
        this.control.target.copy(center)

        // 自动设置方向光的位置
        const lightOffset = maxDim * 1.5 // 光源离模型中心的距离
        const directionalLight = new DirectionalLight(0xffffff, 5)
        directionalLight.position.set(
            center.x + lightOffset,
            center.y + lightOffset,
            center.z + lightOffset
        ) // 从对角线方向照射模型
        directionalLight.castShadow = true // 开启阴影
        directionalLight.lookAt(center)
        this.scene.add(directionalLight)

        // 设置方向光的阴影属性
        // directionalLight.shadow.mapSize.width = 2048;
        // directionalLight.shadow.mapSize.height = 2048;
        // directionalLight.shadow.camera.near = 0.5;
        // directionalLight.shadow.camera.far = lightOffset * 3;  // 根据光源的距离动态设置 far 剪裁面
    }

    public dispose(): void {
        this.renderer.dispose()
        this.control.dispose()
        // this.scene.dispose();
        this.model?.traverse(node => {
            if (node instanceof Mesh) {
                node.geometry.dispose()
                node.material.dispose()
            }
        })
    }

    public onWindowResize(): void {
        // 更新相机的宽高比
        this.camera.aspect = this.config.dom.clientWidth / this.config.dom.clientHeight
        this.camera.updateProjectionMatrix()
        this.renderer.setSize(this.config.dom.clientWidth, this.config.dom.clientHeight)
    }

    public async loadModel(modelUrl: string, mtlUrl?: string): Promise<void> {
        if (!this.model) {
            //! 加载模型 通过后缀判断模型类型并加载
            const modelType = modelUrl.split('.').pop()!.toLowerCase()
            switch (modelType) {
                case 'gltf':
                case 'glb': {
                    const loader = new GLTFLoader()
                    const gltf = await loader.loadAsync(modelUrl)
                    this.model = gltf.scene
                    // this.model.rotation.y = Math.PI / 2
                    this.scene.add(this.model)
                    this.scene.traverse(node => {
                        if (node instanceof Mesh) {
                            node.castShadow = true
                            node.receiveShadow = true
                            if (!node.material.map) {
                                // 如果没有贴图，设置默认材质
                                node.material = new MeshStandardMaterial({ color: 0xaaaaaa })
                            }
                        }
                    })
                    break
                }
                case 'fbx': {
                    const loader = new FBXLoader()
                    const model = await loader.loadAsync(modelUrl)
                    this.model = model
                    this.scene.add(this.model)
                    //加载动画
                    if (model.animations && model.animations.length > 0) {
                        //! 播放动画
                        const mixer = new AnimationMixer(model)
                        mixer.clipAction(model.animations[0]).play()
                        this.mixer = mixer
                    }
                    break
                }
                case 'obj': {
                    const mtlLoader = new MTLLoader()
                    const loader = new OBJLoader()

                    if (mtlUrl) {
                        const mtl = await mtlLoader.loadAsync(mtlUrl!)
                        loader.setMaterials(mtl)
                    }
                    const obj = await loader.loadAsync(modelUrl)
                    this.model = obj
                    this.scene.add(this.model)
                    this.scene.traverse(node => {
                        if (node instanceof Mesh) {
                            node.castShadow = true
                            node.receiveShadow = true
                            if (!node.material.map) {
                                // 如果没有贴图，设置默认材质
                                node.material = new MeshStandardMaterial({ color: 0xaaaaaa })
                            }
                        }
                    })
                    break
                }
                default:
                    console.error('模型格式不正确')
            }
        } else {
            console.error('模型已加载')
        }
    }
}
