<template>
    <div class="relic-infos-box">
        <img class="demo-relic-img" src="@/assets/images/demo/relic_1.png" />
        <div class="relic-back" @click="emit('close')"></div>
        <div class="model-box" id="relicModel"></div>
    </div>
</template>

<script setup lang="ts">
import ModelViewer from '@/components/modelViewer/ModelViewer'
const emit = defineEmits(['close'])

onMounted(() => {
    let modelUrl = `${import.meta.env.VITE_WDS_STATIC_PATH}/WW_gltf/WW_gltf.gltf`
    const modelViewer = new ModelViewer({
        dom: document.getElementById('relicModel'),
        modelUrl,
        autoRotate: true
    })
    modelViewer.loadModel(modelUrl).then(() => {
        modelViewer.placeCameraAndLight()
    })
})
</script>

<style lang="scss" scoped>
.relic-infos-box {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9999;
    .demo-relic-img {
        width: 100%;
        height: 100%;
    }
    .relic-back {
        position: absolute;
        left: 150px;
        top: 100px;
        cursor: pointer;
        width: 260px;
        height: 160px;
    }
    .model-box {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 1500px;
        height: 1500px;
    }
}
</style>
