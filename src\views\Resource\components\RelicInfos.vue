<template>
    <div class="relic-infos-box">
        <img
            class="relic-back"
            @click="emit('close')"
            src="@/assets/images/resource/relic_back.png"
        />
        <div class="page-title">
            <div class="title-text">数字文物库</div>
            <img src="@/assets/images/resource/title_line.png" />
        </div>
        <div class="relic-types">
            <div
                class="type-item"
                v-for="item in relicTypes"
                :key="item.id"
                :class="{ active: currentRelicType == item.id }"
                @click="changeRelicType(item.id)"
            >
                {{ item.name }}
            </div>
        </div>
        <img class="bg-top" src="@/assets/images/resource/bg_relic_4.png" />
        <div class="relic-content">
            <div class="relic-item-small">
                <img class="relic-item-image" src="@/assets/images/resource/relic_1.png" />
            </div>
            <div class="relic-item-small">
                <img class="relic-item-image" src="@/assets/images/resource/relic_2.png" />
            </div>
            <div class="relic-item-large" id="relicModel">
                <img class="relic-arrow relic-prev" src="@/assets/images/resource/relic_prev.png" />
                <img class="relic-arrow relic-next" src="@/assets/images/resource/relic_next.png" />
            </div>
            <div class="relic-item-small">
                <img class="relic-item-image" src="@/assets/images/resource/relic_3.png" />
            </div>
            <div class="relic-item-small">
                <img class="relic-item-image" src="@/assets/images/resource/relic_4.png" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import ModelViewer from '@/components/modelViewer/ModelViewer'
const emit = defineEmits(['close'])
const staticBasePath = window.webConfig.staticResourceUrl
onMounted(() => {
    let modelUrl = `${staticBasePath}/WW_gltf/WW_gltf.gltf`
    const modelViewer = new ModelViewer({
        dom: document.getElementById('relicModel'),
        modelUrl,
        autoRotate: true
    })
    modelViewer.loadModel(modelUrl).then(() => {
        modelViewer.placeCameraAndLight()
    })
})
const currentRelicType = ref(1)
const relicTypes = ref([
    { name: '造像', id: 1 },
    { name: '法器', id: 2 },
    { name: '彩绘', id: 3 },
    { name: '石刻', id: 4 },
    { name: '碑刻', id: 5 },
    { name: '藏品', id: 6 }
])
const changeRelicType = type => {
    currentRelicType.value = type
}
</script>

<style lang="scss" scoped>
.relic-infos-box {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9999;
    background: url('@/assets/images/resource/bg_relic_1.png') center center/ 100% 100% no-repeat;
    .relic-back {
        position: absolute;
        left: 200px;
        top: 130px;
        width: 191.885px;
        height: 90.117px;
        cursor: pointer;
    }
    .page-title {
        position: absolute;
        left: 200px;
        top: 50%;
        transform: translateY(-50%);
        color: #5f3d1d;
        font-size: 120px;
        font-weight: 600;
        line-height: 150px;
        letter-spacing: 50px;
        display: flex;
        align-items: center;
        .title-text {
            writing-mode: vertical-lr;
            margin-right: 40px;
        }
        img {
            width: 57px;
            height: 1027px;
        }
    }
    .relic-types {
        display: flex;
        position: absolute;
        left: 50%;
        bottom: 130px;
        transform: translateX(-50%);
        border-bottom: 1px solid #c39d67aa;
        .type-item {
            position: relative;
            text-align: center;
            width: 595px;
            font-size: 70px;
            font-weight: 600;
            color: #c39d67;
            cursor: pointer;
            padding-bottom: 20px;
            transition: all 0.3s ease-in-out;
            &::after {
                content: '';
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                width: 15px;
                height: 15px;
                background: transparent;
                border: 1px solid #c39d67;
                border-radius: 50%;
                bottom: -11px;
            }
            &.active {
                color: #422500;
                border-bottom: 1px solid #422500;
                &::after {
                    background: #422500;
                    border: 1px solid #422500;
                }
            }
        }
    }
    .bg-top {
        width: 1500px;
        position: absolute;
        left: calc(50% + 13px);
        top: 0;
        transform: translateX(-50%);
    }
    .relic-content {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        .relic-item-small {
            width: 987px;
            height: 987px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding-bottom: 220px;
            background: url('@/assets/images/resource/bg_relic_3.png') center center/ 100% 100%
                no-repeat;
            & + .relic-item-small {
                margin-left: 200px;
            }
            .relic-item-image {
                width: auto;
                height: 90%;
            }
        }
        .relic-item-large {
            position: relative;
            width: 1708px;
            height: 1708px;
            background: url('@/assets/images/resource/bg_relic_2.png') center center/ 100% 100%
                no-repeat;
            margin: 0 600px;
            .relic-arrow {
                width: 290px;
                height: 200px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
            }
            .relic-prev {
                left: -420px;
            }
            .relic-next {
                right: -420px;
            }
        }
    }
}
</style>
