<template>
    <div class="heritage-style-box right-panel-content">
        <div class="info-title common-gradient-text">遗产风貌</div>
        <div class="info-subtitle">——九宫、八观、三十二庵堂、七十二岩庙</div>
        <div class="info-introduce">
            武当山古建筑群始建于唐贞观年间（627—649年），宋、元时期均有建设。明永乐十年（1412年），明成祖朱棣在武当山敕建宫观殿堂，兴建了以金顶为核心的大批道教宫、观、祠、庙，形成了九宫、八观、三十六庵堂、七十二岩庙的古建筑群体系。现存古建筑49处，这些建筑集中体现了中国元、明、清三代世俗和宗教建筑的建筑学和艺术成就。
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text" style="letter-spacing: 4px">主要建筑遗产</span>
        </div>
        <div class="info-content-list">
            <div class="card-list-container">
                <div class="card-list">
                    <div
                        class="card-row"
                        v-for="(row, rowIndex) in currentPageData"
                        :key="rowIndex"
                    >
                        <div
                            class="card-item"
                            v-for="item in row"
                            :key="item.value"
                            @click="onCardClick(item)"
                        >
                            <div class="card-item-cover"></div>
                            <div class="card-item-name">{{ item.name }}</div>
                        </div>
                    </div>
                </div>
                <img
                    class="prev-icon"
                    src="@/assets/images/common/arrow_left.png"
                    @click="prevPage"
                    :class="{ disabled: currentPage === 0 }"
                />
                <img
                    class="next-icon"
                    src="@/assets/images/common/arrow_right.png"
                    @click="nextPage"
                    :class="{ disabled: currentPage === totalPages - 1 }"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useDasUE } from '@/hooks/useDasUEHook'
import { buildingData } from '@/config/buildingData'

let ueManager = null
let pointsLayer = ref([]) // 点位图层 pointsLayer
const heritageList = ref(buildingData)
const currentPage = ref(0)
const itemsPerPage = 10 // 每页10个项目
const itemsPerRow = 5 // 每行5个项目

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.resetCamera()
        ueManager.dasSelectTool.initSelectTool({
            onClick: obj => onBulidingMarkerClick(obj)
        })
        addPoints()
    })
})

onBeforeUnmount(async () => {
    ueManager?.clearAllLayerAndEvent()
})

// 添加建筑点位
const addPoints = async () => {
    if (!ueManager) return
    let datas = heritageList.value.filter(item => item.points?.length > 0)
    const layers = await ueManager.dasPointsLayer.batchCreateInstance(
        datas.map(item => ({
            ...item,
            pointSize: [200, 50],
            enableDepthTest: false
        }))
    )
    pointsLayer.value = layers.map((layer, index) => {
        layer.customData = datas[index]
        return layer
    })
}

// 标注点击事件
const onBulidingMarkerClick = obj => {
    const item = obj.message ? JSON.parse(obj.message) : {}
    if (item.selectLayer?.class == 'DasPointsLayer') {
        pointsLayer.value.forEach(layer => {
            if (layer.id == item.selectLayer.id) {
                // ElMessageBox.alert('点击了点位：' + layer.customData.name, '标注点击事件')
            }
        })
    }
}
// 点击列表卡片 定位
const onCardClick = item => {
    pointsLayer.value.forEach(layer => {
        if (layer.customData.value == item.value) {
            layer.flyToThis()
        }
    })
}

// 计算总页数
const totalPages = computed(() => {
    return Math.ceil(heritageList.value.length / itemsPerPage)
})

// 计算当前页的数据，分为两行
const currentPageData = computed(() => {
    const startIndex = currentPage.value * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const pageItems = heritageList.value.slice(startIndex, endIndex)

    // 将数据分为两行，每行5个
    const rows = []
    for (let i = 0; i < pageItems.length; i += itemsPerRow) {
        rows.push(pageItems.slice(i, i + itemsPerRow))
    }
    return rows
})

// 上一页
const prevPage = () => {
    if (currentPage.value > 0) {
        currentPage.value--
    }
}

// 下一页
const nextPage = () => {
    if (currentPage.value < totalPages.value - 1) {
        currentPage.value++
    }
}
</script>

<style lang="scss" scoped>
.heritage-style-box {
    height: 100%;
    text-align: right;
}
</style>
