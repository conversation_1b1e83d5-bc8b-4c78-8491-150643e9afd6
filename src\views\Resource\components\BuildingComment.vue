<template>
    <div class="building-comment-container">
        <div class="comment-title">「遇真宫」评论互动（390条）</div>
        <div class="comment-input-box">
            <img class="comment-input-icon" src="@/assets/images/common/icon_comment.png" />
            <el-input class="comment-input" v-model="inputComment" placeholder="说两句吧..." />
            <div class="common-toggle-btn" @click="isShowAllComment = !isShowAllComment">
                <img
                    class="toggle-icon"
                    :class="{ isDown: isShowAllComment }"
                    src="@/assets/images/common/arrow_up.png"
                />
                {{ isShowAllComment ? '收起评论' : '展开评论' }}
            </div>
        </div>
        <div class="comment-list" :class="{ isHide: !isShowAllComment }">
            <vue3-seamless-scroll
                :singleHeight="0"
                :list="commentList"
                :hover="true"
                v-if="commentList?.length > 0"
            >
                <div class="comment-item" v-for="(item, index) in commentList" :key="index">
                    {{ item.username }}: {{ item.content }}
                </div>
            </vue3-seamless-scroll>
        </div>
        <div class="audio-description" :class="{ isHide: !isShowAllDetail }">
            <div class="audio-btn">
                <AudioButton :audioSrc="audioSrc" />
            </div>
            <span class="description-text">{{ descriptionText }}</span>
            <div class="common-toggle-btn" @click="isShowAllDetail = !isShowAllDetail">
                <img
                    class="toggle-icon"
                    :class="{ isDown: isShowAllDetail }"
                    src="@/assets/images/common/arrow_up.png"
                />
                {{ isShowAllDetail ? '收起解说' : '展开解说' }}
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import AudioButton from '@/components/audioBar/AudioButton.vue'
let inputComment = ref('')
let isShowAllComment = ref(true) // 是否显示全部评论
let isShowAllDetail = ref(false) // 是否显示全部介绍详情

const commentList = ref([
    { username: '用户1', content: '是我从未见过的遇真宫是我从未见过的遇真宫是我从未见过的遇真宫' },
    { username: '用户2', content: '是我从未见过的遇真宫，好新' },
    { username: '用户3', content: '是我从未见过的遇真宫，好新' },
    { username: '用户4', content: '是我从未见过的遇真宫，好新' },
    { username: '用户5', content: '是我从未见过的遇真宫，好新' },
    { username: '用户6', content: '是我从未见过的遇真宫，好新' },
    { username: '用户7', content: '是我从未见过的遇真宫，好新' },
    { username: '用户8', content: '是我从未见过的遇真宫，好新' },
    { username: '用户9', content: '是我从未见过的遇真宫，好新' },
    { username: '用户10', content: '是我从未见过的遇真宫，好新' }
]) // 评论列表

const descriptionText = ref(
    '武当山遇真宫的建筑布局严格遵循道教"天人合一"理念，呈坐北朝南的轴线对称格局。主体建筑沿中轴线依次布置山门、龙虎殿、真仙殿、配殿等，形成三进院落空间，暗合"三才"之道。其布局特点有三：一是前宫后寝的礼制序列，真仙殿与张三丰修道区形成神圣空间递进；二是"负阴抱阳"的风水格局，背靠凤凰山，面朝九龙山，左右溪流环抱；三是官式建筑与山地营建的巧妙结合，台基依山势层层抬升，既显皇家气派又顺应自然地形。建筑群整体呈现出明代道教宫观"象天法地"的典型特征。武当山遇真宫的建筑布局严格遵循道教"天人合一"理念，呈坐北朝南的轴线对称格局。武当山遇真宫的建筑布局严格遵循道教"天人合一"理念，呈坐北朝南的轴线对称格局。主体建筑沿中轴线依次布置山门、龙虎殿、真仙殿、配殿等，形成三进院落空间，暗合"三才"之道。'
)
const audioSrc = ref('')
</script>
<style lang="scss" scoped>
.building-comment-container {
    position: absolute;
    width: 807px;
    z-index: 1000;
    left: 108px;
    bottom: 800px;
    border-radius: 8px;
    background: rgba(33, 32, 29, 0.8);
    backdrop-filter: blur(4px);
    padding: 20px 40px;
    color: #fff;
    .comment-title {
        font-size: 33.5px;
        margin-bottom: 20px;
    }
    .comment-input-box {
        position: relative;
        width: 100%;
        height: 67px;
        :deep(.comment-input) {
            background: #fff9f0;
            border-radius: 14px;
            font-size: 26px;
            width: 100%;
            height: 100%;
            padding: 0 140px 0 40px;
            .el-input__wrapper {
                height: 100%;
                background-color: transparent;
                box-shadow: none;
                color: #4a300f;
            }
        }
        .comment-input-icon {
            position: absolute;
            left: 18px;
            top: 15px;
            width: 37.468px;
            height: 35.387px;
            z-index: 9;
        }
    }
    .common-toggle-btn {
        position: absolute;
        font-size: 23px;
        right: 12px;
        top: 15px;
        width: 167.431px;
        height: 40.183px;
        background: #4a300f;
        border-radius: 33px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .toggle-icon {
            width: 15px;
            height: 12px;
            margin-right: 12px;
            transition: all 0.5s ease;
            transform: rotate(180deg);
            &.isDown {
                transform: rotate(0);
            }
        }
    }
    .comment-list {
        height: 330px;
        overflow: hidden;
        transition: all 0.5s ease;
        margin: 20px 0;
        &.isHide {
            height: 0;
        }
        .comment-item {
            height: 67px;
            line-height: 67px;
            max-width: 100%;
            border-radius: 13.394px;
            background: #000;
            margin-top: 20px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            padding: 0 20px;
        }
    }
    .audio-description {
        position: relative;
        width: 100%;
        border-radius: 13.394px;
        background: #fff9f0;
        font-size: 27px;
        padding: 28px 20px;
        height: 330px;
        overflow-y: auto;
        transition: all 0.5s ease;
        display: flex;
        align-items: flex-start;
        .audio-btn {
            width: 35px;
            height: 35px;
            margin-right: 20px;
        }
        .description-text {
            color: #4a300f;
            display: inline-block;
            width: 65%;
            text-align: justify;
        }
        &.isHide {
            height: 100px;
            .description-text {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .common-toggle-btn {
            top: 30px;
        }
    }
}
</style>
