{"name": "wudang-display", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/", "prepare": "husky"}, "dependencies": {"amfe-flexible": "^2.2.1", "echarts": "^5.6.0", "element-plus": "^2.9.9", "eslint-plugin-prettier": "^5.4.0", "pinia": "^3.0.3", "postcss-pxtorem": "^5.1.1", "sass": "^1.87.0", "sass-loader": "^16.0.5", "swiper": "^11.2.8", "three": "^0.177.0", "typescript-eslint": "^8.32.0", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite-plugin-vue-devtools": "^7.7.6", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-router": "^4.5.1", "vue3-seamless-scroll": "^3.0.2"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-vue": "^10.1.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "pinia-plugin-persist": "^1.0.0", "prettier": "^3.5.3", "terser": "^5.39.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}