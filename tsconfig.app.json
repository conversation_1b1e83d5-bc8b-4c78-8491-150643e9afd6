{
    "compilerOptions": {
        "baseUrl": "./",
        "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
        /* Linting */
        // "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedSideEffectImports": true,
        "target": "ESNext",
        "module": "ESNext",
        "moduleResolution": "node",
        "noImplicitThis": true,
        "paths": {
            "@/*": ["src/*"]
        }
    },
    "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts"],
    "exclude": ["node_modules", "dist", "**/*.js"],
    "lib": ["esnext", "dom"],
    "allowJs": true
}
