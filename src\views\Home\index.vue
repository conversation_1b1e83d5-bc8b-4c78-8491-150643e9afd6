<template>
    <div class="home-container">
        <div class="module-title" v-if="moduleTitle">{{ moduleTitle }}</div>
        <RightPanelBox>
            <template #content>
                <RegionFeature v-if="currentNav === 'regionFeature'" />
                <MountainLayout v-else-if="currentNav === 'mountainLayout'" />
                <RouteLines
                    v-else-if="currentNav === 'routeLines'"
                    @change-module-title="changeModuleTitle"
                />
                <HeritageStyle v-else-if="currentNav === 'heritageStyle'" />
                <BuildingLayout v-else-if="currentNav === 'buildingLayout'" />
            </template>
        </RightPanelBox>
        <div class="legend-content">
            <LegendPanel />
        </div>
        <BottomMenu type="home" @change-nav="onChangeNav" />
    </div>
</template>

<script setup lang="ts">
import { useNavStore } from '@/stores/nav'
import LegendPanel from '@/components/commonPanel/LegendPanel.vue'
import BottomMenu from '@/components/tools/BottomMenu.vue'
import RightPanelBox from '@/components/commonPanel/RightPanelBox.vue'
import RegionFeature from './components/RegionFeature.vue'
import MountainLayout from './components/MountainLayout.vue'
import RouteLines from './components/RouteLines.vue'
import HeritageStyle from './components/HeritageStyle.vue'
import BuildingLayout from './components/BuildingLayout.vue'

const navStore = useNavStore()
const currentNav = computed(() => navStore.currentNav) // 当前选中的二级菜单
const moduleTitle = ref('')
onMounted(() => {})

onBeforeUnmount(async () => {})
// 切换子菜单
const onChangeNav = (type: string) => {
    moduleTitle.value = ''
    switch (type) {
        case 'regionFeature':
            break
        case 'mountainLayout':
            moduleTitle.value = '七十二峰朝大顶'
            break
        case 'routeLines':
            break
        case 'heritageStyle':
            moduleTitle.value = '遗产风貌'
            break
        case 'buildingLayout':
            break
    }
}

const changeModuleTitle = (title: string) => {
    moduleTitle.value = title
}
</script>

<style scoped lang="scss">
.home-container {
    .module-title {
        position: absolute;
        z-index: 1000;
        left: 50%;
        top: 210px;
        transform: translateX(-50%);
        width: 724px;
        height: 195px;
        background: url('@/assets/images/common/module_title.png') center center / 520px 100%
            no-repeat;
        color: #fff;
        font-size: 60px;
        text-align: center;
        line-height: 195px;
        font-weight: 600;
        letter-spacing: 5px;
    }

    .legend-content {
        position: absolute;
        bottom: 74px;
        right: 2288px;
    }
}
</style>
