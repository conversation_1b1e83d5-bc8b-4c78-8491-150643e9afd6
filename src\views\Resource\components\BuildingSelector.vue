<template>
    <div class="building-selector-box">
        <img class="left-mist-icon" src="@/assets/images/common/icon_mist_left.png" />
        <img class="right-mist-icon" src="@/assets/images/common/icon_mist_right.png" />
        <div class="building-list-container">
            <Swiper
                class="building-list"
                :slides-per-view="7"
                :space-between="17"
                :navigation="{
                    prevEl: '.swiper-prev-icon',
                    nextEl: '.swiper-next-icon'
                }"
                :modules="swiperModules"
            >
                <SwiperSlide
                    class="building-item"
                    :class="{ active: currentId == item.id }"
                    v-for="item in buildingData"
                    :key="item.id"
                    @click="changeBuilding(item.id)"
                >
                    <div class="building-item-cover"></div>
                    <div class="building-item-name">{{ item.name }}</div>
                </SwiperSlide>
            </Swiper>
            <img class="swiper-prev-icon" src="@/assets/images/common/arrow_left.png" />
            <img class="swiper-next-icon" src="@/assets/images/common/arrow_right.png" />
        </div>
    </div>
</template>
<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Navigation } from 'swiper/modules'
import 'swiper/css/navigation'
const swiperModules = [Navigation]

const currentId = ref(1)
const buildingData = ref([
    { name: '山门', id: 1 },
    { name: '山门山门山门山门山门山门山门山门', id: 2 },
    { name: '山门', id: 3 },
    { name: '山门', id: 4 },
    { name: '山门', id: 5 },
    { name: '山门', id: 6 },
    { name: '山门', id: 7 },
    { name: '山门', id: 8 },
    { name: '山门', id: 9 },
    { name: '山门', id: 10 }
])

const changeBuilding = id => {
    currentId.value = id
}
</script>
<style scoped lang="scss">
.building-selector-box {
    position: absolute;
    width: 3000px;
    height: 450px;
    z-index: 1000;
    left: 50%;
    bottom: 97px;
    transform: translateX(-50%);
    background: rgba(33, 32, 29, 0.8);
    backdrop-filter: blur(8px);
    border-radius: 8px;
    padding: 95px 150px;
    .left-mist-icon {
        position: absolute;
        width: 136px;
        height: 129px;
        top: -30px;
        left: -90px;
    }
    .right-mist-icon {
        position: absolute;
        width: 136px;
        height: 129px;
        top: -30px;
        right: -90px;
    }
    .building-list-container {
        position: relative;
        .building-list {
            .building-item {
                height: 100%;
                color: #fff;
                text-align: center;
                cursor: pointer;
                .building-item-cover {
                    height: 200px;
                    background: rgba(255, 255, 255, 0.2);
                }
                .building-item-name {
                    margin-top: 20px;
                    font-size: 30px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
                &.active {
                    .building-item-cover {
                        border: 9px solid #ebd8b4;
                    }
                }
            }
        }
        .swiper-next-icon {
            right: -110px;
            background: url('@/assets/images/common/arrow_right.png') center center / 100% 100%
                no-repeat;
        }
        .swiper-prev-icon {
            left: -110px;
            background: url('@/assets/images/common/arrow_left.png') center center / 100% 100%
                no-repeat;
        }
        .swiper-prev-icon,
        .swiper-next-icon {
            position: absolute;
            cursor: pointer;
            width: 57px;
            height: 55px;
            top: 75px;
            &::after {
                content: '';
            }
        }
    }
}
</style>
