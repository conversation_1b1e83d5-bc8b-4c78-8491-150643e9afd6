import Viewer from '@/components/dasUE/Viewer'
import DasScene from '@/components/dasUE/scene/DasScene'
import DasPointsLayer from '@/components/dasUE/layer/DasPointLayer'
import DasGroupLayer from '@/components/dasUE/layer/DasGroupLayer'
import DasWidgetBillboardLayer from '@/components/dasUE/layer/DasWidgetBillboardLayer'
import DasUIBillboardLayer from '@/components/dasUE/layer/DasUIBillboardLayer'
import DasPolylineLayer from '@/components/dasUE/layer/DasPolylineLayer'
import DasInnerLayer from '@/components/dasUE/layer/DasInnerLayer'
import DasPointsTool from '@/components/dasUE/tool/DasPointTool'
import DasSelectTool from '@/components/dasUE/tool/DasSelectTool'
import DasPolylineTool from '@/components/dasUE/tool/DasPolylineTool'

const signalServerUrl = import.meta.env.VITE_SIGNAL_SERVER_URL
export default class DasUE {
    public viewer
    public isViewerReady = false // Viewer initialized flag
    public dasScene: DasScene
    public dasGroupLayer: DasGroupLayer
    public dasPointsLayer: DasPointsLayer
    public dasWidgetBillboardLayer: DasWidgetBillboardLayer
    public dasUIBillboardLayer: DasUIBillboardLayer
    public dasPolylineLayer: DasPolylineLayer
    public dasInnerLayer: DasInnerLayer
    public dasPointsTool: DasPointsTool
    public dasSelectTool: DasSelectTool
    public dasPolylineTool: DasPolylineTool

    constructor(domId: string) {
        this.viewer = new Viewer({
            // InputControllers: [],
            onInitialize: () => {
                console.log('~~~~~~~~~~ Viewer initialized ~~~~~~~~~~')
                this.isViewerReady = true
            },
            useUrlParams: false,
            hideDefaultUI: true,
            signalServer: signalServerUrl
        })
        const targetDom = document.getElementById(domId)
        targetDom.appendChild(this.viewer.rootElement)
        this.dasScene = new DasScene() // Scene控制（Weather、LevelSequence、Camera、Scene）
        this.dasGroupLayer = new DasGroupLayer() // 图层组
        this.dasPointsLayer = new DasPointsLayer() // 点位图层
        this.dasWidgetBillboardLayer = new DasWidgetBillboardLayer() // 标牌图层
        this.dasUIBillboardLayer = new DasUIBillboardLayer() // UI标牌图层
        this.dasPolylineLayer = new DasPolylineLayer() // 折线图层
        this.dasInnerLayer = new DasInnerLayer() // 内置图层
        this.dasPointsTool = new DasPointsTool() // 点位工具
        this.dasSelectTool = new DasSelectTool() // 选择工具
        this.dasPolylineTool = new DasPolylineTool() // 折线工具
    }

    // 复位视角
    resetCamera() {
        this.dasScene.flyToLocationLLH(
            [110.97293599724767, 32.50891027717211, 43495.0802548074],
            {
                pitch: -74.76436942462497,
                yaw: 15.90950516198304,
                roll: 0
            },
            1
        )
    }

    // 清除所有图层
    async clearAllLayers() {
        const rootGroup = await this.dasGroupLayer.getRoot()
        rootGroup.removeAllLayer()
    }
    // 清除所有图层、事件监听
    async clearAllLayerAndEvent() {
        const rootGroup = await this.dasGroupLayer.getRoot()
        rootGroup.removeAllLayer()
        this.dasSelectTool.removeCallBack()
        this.dasSelectTool.finishTool()
    }
}
