import { createRouter, createWebHistory } from 'vue-router'

// 1. 基础路由（无需权限，如登录页）
const baseRoutes = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/Login/index.vue')
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('@/views/404/NotFound.vue')
    }
]

// 2. 权限路由（需动态过滤，嵌套在 Layout 下）
const permissionRoutes = {
    path: '/', // 父路由路径
    redirect: '/home',
    component: () => import('@/layout/index.vue'), // 共用布局
    children: [
        {
            path: 'home', // 子路由路径为空时匹配主页
            name: 'home',
            component: () => import('@/views/Home/index.vue'),
            meta: { permission: 'home' } // 所需权限字符
        },
        {
            path: 'resource',
            name: 'resource',
            component: () => import('@/views/Resource/index.vue'),
            meta: { permission: 'resource' }
        },
        {
            path: 'application',
            name: 'application',
            component: () => import('@/views/Application/index.vue'),
            meta: { permission: 'application' }
        },
        {
            path: 'scene',
            name: 'scene',
            component: () => import('@/views/Resource/components/BuildingScene.vue'),
            meta: { permission: 'resource' }
        },
        {
            path: 'demo',
            name: 'demo',
            component: () => import('@/views/demo/index.vue'),
            meta: { permission: 'demo' }
        }
    ]
}

// 3. 创建路由器实例（初始仅包含基础路由）
const router = createRouter({
    history: createWebHistory(),
    routes: baseRoutes
})

// 4. 动态添加权限路由的函数
export function addPermissionRoutes(permissions) {
    // 过滤出用户有权限的子路由
    const allowedChildren = permissionRoutes.children.filter(route =>
        permissions.includes(route.meta.permission)
    )

    // 若有允许的子路由，添加父路由（包含 Layout 和过滤后的子路由）
    if (allowedChildren.length > 0) {
        router.addRoute({
            ...permissionRoutes,
            children: allowedChildren // 替换为有权限的子路由
        })
    }
}

//? 测试代码
addPermissionRoutes(['home', 'application', 'resource', 'demo'])

export default router
