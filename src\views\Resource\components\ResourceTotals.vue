<template>
    <div class="resource-totals-box right-panel-content">
        <div class="info-title common-gradient-text">武当文化资源库</div>
        <div class="info-totals">
            <div class="total-item" v-for="item in totalDatas" :key="item.label">
                <div class="total-circle">
                    <div class="total-value">
                        <span class="total-number">{{ item.value }}</span>
                        <span class="total-unit">{{ item.unit }}</span>
                    </div>
                </div>
                <div class="total-label">{{ item.label }}</div>
            </div>
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">资源分布</span>
        </div>
        <div class="info-items">
            <div class="info-items-row">
                <div class="info-item" v-for="item in resourceInfos.slice(0, 3)" :key="item.name">
                    <div class="info-item-value">
                        <span class="value-number">{{ item.value }}</span>
                        <span class="value-unit">{{ item.unit }}</span>
                    </div>
                    <div class="info-item-label">{{ item.name }}</div>
                </div>
            </div>
            <div class="info-items-row">
                <div class="info-item" v-for="item in resourceInfos.slice(3, 6)" :key="item.name">
                    <div class="info-item-value">
                        <span class="value-number">{{ item.value }}</span>
                        <span class="value-unit">{{ item.unit }}</span>
                    </div>
                    <div class="info-item-label">{{ item.name }}</div>
                </div>
            </div>
        </div>
        <div class="info-content-title">
            <span class="info-content-title-text">资源更新</span>
        </div>
        <div ref="chartRef" class="chart-box"></div>
        <PalaceInfo
            v-if="isShowPalaceModal"
            @close="closePalaceModal"
            @go-palace="goPalace"
            :detailData="currentPalace"
        />
    </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { useDasUE } from '@/hooks/useDasUEHook'
import { useRouter } from 'vue-router'
import PalaceInfo from './PalaceInfo.vue'
import { buildingData } from '@/config/buildingData'

let ueManager = null
let pointsLayer = ref([]) // 点位图层 pointsLayer
let isShowPalaceModal = ref(false)
let currentPalace = ref(null)
let router = useRouter()

const chartRef = ref()
const resourceInfos = ref([
    { name: '实景三维', value: 312, unit: 'km²' },
    { name: '空间专题资源', value: 400, unit: '个' },
    { name: '高精度文物模型', value: 1612, unit: '件' },
    { name: '古籍档案数字化', value: 400, unit: '万页' },
    { name: '壁画彩绘拓片', value: 1612, unit: 'm²' },
    { name: '场景演绎视频', value: 400, unit: '分钟' }
])

const totalDatas = ref([
    { label: '数字化覆盖率', value: '95', unit: '%' },
    { label: '资源存储量', value: '10.7', unit: 'tb' },
    { label: '资源数量', value: '230', unit: '万条' }
])

// 资源更新数据
const chartData = ref([
    { month: '2024-12', value: 142 },
    { month: '2025-01', value: 122 },
    { month: '2025-02', value: 242 },
    { month: '2025-03', value: 342 },
    { month: '2025-04', value: 242 },
    { month: '2025-05', value: 342 },
    { month: '2025-06', value: 242 }
])

const buildingList = ref(buildingData)
// 初始化图表
const initChart = () => {
    if (!chartRef.value) return
    chartRef.value?.removeAttribute('_echarts_instance_')
    let chartInstance = echarts.init(chartRef.value)
    const option = {
        backgroundColor: 'transparent',
        grid: {
            left: '12%',
            right: '3%',
            top: '25%',
            bottom: '1%',
            containLabel: true
        },
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(225, 187, 130, 0.4)',
            borderColor: '#EEC173',
            padding: 20,
            textStyle: {
                fontSize: 20,
                color: '#ffffff'
            }
        },
        xAxis: {
            type: 'category',
            data: chartData.value.map(item => item.month),
            boundaryGap: true, // 让线条从坐标轴开始，不留空隙
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#A3A3A3',
                    width: 1
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#A3A3A3',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: '#FFFFFF40',
                    type: 'dashed',
                    width: 1
                }
            }
        },
        yAxis: {
            name: '单位（万条）',
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#A3A3A3',
                    width: 1
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#A3A3A3',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: '#FFFFFF40',
                    type: 'dashed',
                    width: 1
                }
            }
        },
        series: [
            {
                type: 'line',
                data: chartData.value.map(item => item.value),
                smooth: true,
                symbol: 'emptyCircle',
                symbolSize: 8,
                lineStyle: {
                    color: '#EEC173',
                    width: 2
                },
                itemStyle: {
                    color: '#EEC173',
                    borderColor: '#EEC173',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(225, 187, 130, 0.4)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(225, 187, 130, 0.05)'
                            }
                        ]
                    }
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#EEC173',
                    fontSize: 12,
                    fontFamily: 'Source Han Serif CN'
                }
            }
        ]
    }
    chartInstance.setOption(option)
}

// 添加建筑点位
const addPoints = async () => {
    if (!ueManager) return
    let datas = buildingList.value.filter(item => item.points?.length > 0)
    const layers = await ueManager.dasPointsLayer.batchCreateInstance(
        datas.map(item => ({
            ...item,
            pointSize: [85, 250],
            texturePath: item.markerPath,
            enableDepthTest: false
        }))
    )
    pointsLayer.value = layers.map((layer, index) => {
        layer.customData = datas[index]
        return layer
    })
}

// 点击标注，飞至中场景
const onBulidingMarkerClick = obj => {
    const item = obj.message ? JSON.parse(obj.message) : {}
    if (item.selectLayer?.class == 'DasPointsLayer') {
        // ueManager.dasScene.flyToCameraPositionByName('相机点位1')
        currentPalace.value = pointsLayer.value.find(
            layer => layer.id == item.selectLayer.id
        ).customData
        if (currentPalace.value.name === '遇真宫') {
            let cameraNear: unknown = buildingData.find(item => item.name === '遇真宫').cameraNear
            ueManager.dasScene.flyToLocationLLH(cameraNear.locationLLH, cameraNear.rotationLLH)
        } else {
            let cameraNear: unknown = buildingData.find(item => item.name === '太和宫').cameraNear
            ueManager.dasScene.flyToLocationLLH(cameraNear.locationLLH, cameraNear.rotationLLH)
        }
        setTimeout(() => {
            isShowPalaceModal.value = true
        }, 500)
    }
}

const closePalaceModal = () => {
    isShowPalaceModal.value = false
    currentPalace.value = {}
}

const goPalace = () => {
    router.push(`/scene?palace=${currentPalace.value.name}`)
}

onMounted(() => {
    const { dasUE, onViewerReady } = useDasUE()
    onViewerReady(() => {
        ueManager = dasUE
        ueManager.resetCamera()
        ueManager.dasSelectTool.initSelectTool({
            onClick: obj => onBulidingMarkerClick(obj)
        })
        addPoints()
    })
    nextTick(() => {
        initChart()
    })
})

onBeforeUnmount(() => {
    ueManager?.clearAllLayerAndEvent()
})
</script>

<style lang="scss" scoped>
.resource-totals-box {
    height: 100%;
    text-align: right;

    &.right-panel-content .info-content-title {
        margin-top: 20px;
    }

    .info-item {
        width: 32%;
    }

    .chart-box {
        height: 409px;
        width: 100%;
    }
}
</style>
